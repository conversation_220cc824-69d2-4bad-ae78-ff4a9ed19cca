
export function loadBMap(ak) {
  return new Promise(function(resolve, reject) {
    if (typeof BMapGL !== 'undefined') {
      resolve(BMapGL)
      return true
    }
    window.onBMapCallback = function() {
      resolve(BMapGL)
    }
    let script = document.createElement('script')
    script.type = 'text/javascript'
    // script.src =
    //   'http://api.map.baidu.com/api?v=3.0&ak=' + ak + '&callback=onBMapCallback'
    // script.src =
    //   'http://api.map.baidu.com/api?v=3.0&ak=' + ak + '&callback=onBMapCallback'
    script.src =
      '//api.map.baidu.com/api?v=1.0&type=webgl&ak=HCadBMLcmxQyWBj2jYkhROlaPoTn3kbq&callback=onBMapCallback'
      // script.src =
      // '//api.map.baidu.com/api?type=webgl&v=1.0&ak=' + ak
    script.onerror = reject
    document.head.appendChild(script)
  })
}

// export function add_oval(centre, x, y) {
//   var assemble = new Array();
//   var angle;
//   var dot;
//   var tangent = x / y;
//   for(i = 0; i < 36; i++) {
//       angle = (2 * Math.PI / 36) * i;
//       dot = new BMap.Point(centre.lng + Math.sin(angle) * y * tangent, centre.lat + Math.cos(angle) * y);
//       assemble.push(dot);
//   }
//   return assemble;
// }