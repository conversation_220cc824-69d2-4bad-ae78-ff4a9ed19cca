<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="120px">
      <el-form-item label="所属区域" prop="deptId">
        <treeselect v-model="queryParams.deptId" :options="deptOptions" :show-count="true" placeholder="请选择归属部门"
          style="width:205px" />
      </el-form-item>
      <el-form-item label="机房名称" prop="roomName">
        <el-input v-model="queryParams.roomName" placeholder="请输入机房名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="机房编码" prop="roomCode">
        <el-input v-model="queryParams.roomCode" placeholder="请输入机房编码" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="级别" prop="roomLevel">
        <el-select v-model="queryParams.roomLevel" placeholder="请选择级别" clearable>
          <el-option v-for="dict in dict.type.room_level" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option v-for="dict in dict.type.room_status" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['room:RmRoom:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
          v-hasPermi="['room:RmRoom:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDeletes"
          v-hasPermi="['room:RmRoom:removes']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
          v-hasPermi="['room:RmRoom:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="RmRoomList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column prop="" label="序号" width="80" align="center" type="index" :index="indexMethod" />
      <!-- <el-table-column label="备注说明" align="center" prop="remarks" /> -->
      <el-table-column label="所属区域" align="center" prop="deptId" />
      <el-table-column label="机房名称" align="center" prop="roomName" />
      <el-table-column label="机房编码" align="center" prop="roomCode" />
      <!-- <el-table-column label="机房地址" align="center" prop="address" /> -->
      <!-- <el-table-column label="长度" align="center" prop="length" />
      <el-table-column label="宽度" align="center" prop="width" />
      <el-table-column label="经度" align="center" prop="longitude" />
      <el-table-column label="纬度" align="center" prop="latitud" />
      <el-table-column label="层数" align="center" prop="floorCount" /> -->
      <el-table-column label="级别" align="center" prop="roomLevel">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.room_level" :value="scope.row.roomLevel" />
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.room_status" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['room:RmRoom:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['room:RmRoom:remove']">删除</el-button>
          <el-dropdown>
            <span class="el-dropdown-link" style="padding:5px 10px;">
              <i class="el-icon-setting"></i>
              配置
              <i class="el-icon-arrow-down el-icon--right"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item>
                <el-button size="mini" type="text" @click="handleCabinet(scope.row)">机柜管理</el-button>
              </el-dropdown-item>
              <el-dropdown-item>
                <el-button size="mini" type="text" @click="handleEqu(scope.row)"
                  v-hasPermi="['Performance:WpPerformanceSet:edit']">设备管理</el-button>
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改机房管理对话框 -->
    <el-dialog :title="title" :fullscreen="dialogFull" :visible.sync="open" width="70%" append-to-body
      :close-on-click-modal="false" @close="handleClose()">
      <template slot="title">
        <div class="avue-crud__dialog__header" style="border-bottom:1px solid #DCDFE6;padding-bottom:15px;">
          <span class="el-dialog__title">
            <span
              style="display:inline-block;background-color: #3478f5;width:3px;height:20px;margin-right:5px; float: left;margin-top:2px;margin-right:15px"></span>
            {{ title }}
          </span>
          <div class="avue-crud__dialog__menu" @click="dialogFull ? dialogFull = false : dialogFull = true">
            <i class="el-icon-full-screen"></i>
          </div>
        </div>
      </template>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="16">
            <el-form-item label="机房名称" prop="roomName">
              <el-input v-model="form.roomName" placeholder="请输入机房名称" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="机房编码" prop="roomCode">
              <el-input v-model="form.roomCode" placeholder="请输入机房编码" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="所属区域" prop="deptId">
              <treeselect v-model="form.deptId" :options="deptOptions" :show-count="true" placeholder="请选择归属部门"
                style="width:240px" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="级别" prop="roomLevel">
              <el-select v-model="form.roomLevel" placeholder="请选择级别">
                <el-option v-for="dict in dict.type.room_level" :key="dict.value" :label="dict.label"
                  :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="层数" prop="floorCount">
              <el-input v-model="form.floorCount" placeholder="请输入层数" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="机房地址" prop="address">
              <el-input v-model="form.address" placeholder="请输入机房地址" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="长度(m)" prop="length">
              <el-input v-model="form.length" placeholder="请输入长度" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="宽度(m)" prop="width">
              <el-input v-model="form.width" placeholder="请输入宽度" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="高度(m)" prop="height">
              <el-input v-model="form.height" placeholder="请输入高度" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="经度" prop="longitude">
              <el-input v-model="form.longitude" placeholder="请输入经度" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="纬度" prop="latitud">
              <el-input v-model="form.latitud" placeholder="请输入纬度" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="状态">
              <el-radio-group v-model="form.status">
                <el-radio v-for="dict in dict.type.room_status" :key="dict.value" :label="dict.value">{{ dict.label
                  }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注说明" prop="remarks">
              <el-input v-model="form.remarks" type="textarea" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 添加或修改机柜管理对话框 -->
    <el-dialog :title="title" :fullscreen="dialogFull" :visible.sync="openCab" width="70%" append-to-body
      :close-on-click-modal="false" @close="handleClose()">
      <template slot="title">
        <div class="avue-crud__dialog__header" style="border-bottom:1px solid #DCDFE6;padding-bottom:15px;">
          <span class="el-dialog__title">
            <span
              style="display:inline-block;background-color: #3478f5;width:3px;height:20px;margin-right:5px; float: left;margin-top:2px;margin-right:15px"></span>
            {{ title }}
          </span>
          <div class="avue-crud__dialog__menu" @click="dialogFull ? dialogFull = false : dialogFull = true">
            <i class="el-icon-full-screen"></i>
          </div>
        </div>
      </template>
      <RmCabinet :roomInfo="currentRoomInfo"></RmCabinet>
    </el-dialog>
    <!-- 添加或修改设备管理对话框 -->
    <el-dialog :title="title" :fullscreen="dialogFull" :visible.sync="openEqument" width="70%" append-to-body
      :close-on-click-modal="false" @close="handleClose()">
      <template slot="title">
        <div class="avue-crud__dialog__header" style="border-bottom:1px solid #DCDFE6;padding-bottom:15px;">
          <span class="el-dialog__title">
            <span
              style="display:inline-block;background-color: #3478f5;width:3px;height:20px;margin-right:5px; float: left;margin-top:2px;margin-right:15px"></span>
            {{ title }}
          </span>
          <div class="avue-crud__dialog__menu" @click="dialogFull ? dialogFull = false : dialogFull = true">
            <i class="el-icon-full-screen"></i>
          </div>
        </div>
      </template>
      <RmEquipment :roomInfo="currentRoomInfo"></RmEquipment>
    </el-dialog>
    <!-- <RmEquipment :roomInfo="currentRoomInfo"></RmEquipment> -->
  </div>
</template>

<script>
import { listRmRoom, getRmRoom, delRmRoom, addRmRoom, updateRmRoom } from "@/api/room/RmRoom";
import { deptTreeSelect } from "@/api/system/user";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import RmCabinet from "@/views/cabinet/RmCabinet/index.vue";
import RmEquipment from "@/views/equipment/RmEquipment/index.vue";
export default {
  name: "RmRoom",
  dicts: ['room_status', 'room_level'],
  components: { Treeselect, RmCabinet,RmEquipment },
  data() {
    return {
      //是否弹窗全屏
      dialogFull: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 机房管理表格数据
      RmRoomList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        status: null,
        remarks: null,
        deptId: null,
        roomName: null,
        roomCode: null,
        address: null,
        length: null,
        width: null,
        height: null,
        longitude: null,
        latitud: null,
        floorCount: null,
        roomLevel: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        status: [
          { required: true, message: "状态不能为空", trigger: "blur" }
        ],
        roomName: [
          { required: true, message: "机房名称不能为空", trigger: "blur" }
        ],
        length: [
          { required: true, message: "长度不能为空", trigger: "blur" }
        ],
        width: [
          { required: true, message: "宽度不能为空", trigger: "blur" }
        ],
        roomLevel: [
          { required: true, message: "级别不能为空", trigger: "change" }
        ]
      },
      deptOptions: undefined,
      openCab: false,
      openEqument:false,
      dialogshowSearch: false,
      currentRoomInfo: {},
      RmEquipmentInfo: {},
    };
  },
  created() {
    this.getList();
    this.getDeptTree();
  },
  methods: {
    /** 机柜编辑*/
    handleCabinet(val) {
      this.openCab = true;
      this.dialogFull = true;
      this.title = "机柜管理-" + val.roomName;
      this.currentRoomInfo = val;
    },
    /** 设备编辑*/
    handleEqu(val) {
      this.openEqument = true;
      this.dialogFull = true;
      this.title = "设备管理-" + val.roomName;
      this.currentRoomInfo = val;
    },
    /** 查询部门下拉树结构 */
    getDeptTree() {
      deptTreeSelect().then(response => {
        this.deptOptions = response.data;
      });
    },
    indexMethod(index) {
      return (
        index + (this.queryParams.pageNum - 1) * this.queryParams.pageSize + 1
      );
    },
    handleChange(val) {
      // console.log(val);
    },
    handleClick(tab, event) {
      // console.log(tab, event);
    },
    handleClose() {
      this.cancel();
    },
    /** 查询机房管理列表 */
    getList() {
      this.loading = true;
      listRmRoom(this.queryParams).then(response => {
        this.RmRoomList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.openCab = false;
      this.openEqument=false;
      this.dialogFull = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        uuid: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        userId: null,
        status: "1",
        filetime: null,
        remarks: null,
        delFlag: null,
        deptId: null,
        roomName: null,
        roomCode: null,
        address: null,
        length: null,
        width: null,
        height: null,
        longitude: null,
        latitud: null,
        floorCount: null,
        roomLevel: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.uuid)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "新建";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.uuid || this.ids
      getRmRoom(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.uuid != null) {
            updateRmRoom(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addRmRoom(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDeletes(row) {
      const ids = row.uuid || this.ids;
      this.$modal.confirm('是否确认删除选中的数据项？').then(function () {
        return delRmRoom(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.uuid || this.ids;
      this.$modal.confirm('是否确认删除数据？').then(function () {
        return delRmRoom(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('room/RmRoom/export', {
        ...this.queryParams
      }, `导出明细_${new Date().getTime()}.xlsx`)
    },

  }
};
</script>
<style lang="scss" scoped></style>
