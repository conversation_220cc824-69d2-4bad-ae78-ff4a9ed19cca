import request from '@/utils/request'

// 查询设备槽位模板管理列表
export function listRmEquipmentSlotModel(query) {
  return request({
    url: '/model/RmEquipmentSlotModel/list',
    method: 'get',
    params: query
  })
}

// 查询设备槽位模板管理详细
export function getRmEquipmentSlotModel(id) {
  return request({
    url: '/model/RmEquipmentSlotModel/' + id,
    method: 'get'
  })
}

// 新增设备槽位模板管理
export function addRmEquipmentSlotModel(data) {
  return request({
    url: '/model/RmEquipmentSlotModel',
    method: 'post',
    data: data
  })
}

// 修改设备槽位模板管理
export function updateRmEquipmentSlotModel(data) {
  return request({
    url: '/model/RmEquipmentSlotModel',
    method: 'put',
    data: data
  })
}

// 删除设备槽位模板管理
export function delRmEquipmentSlotModel(id) {
  return request({
    url: '/model/RmEquipmentSlotModel/' + id,
    method: 'delete'
  })
}
