import request from '@/utils/request'

// 查询机柜模板管理列表
export function listRmCabinetModel(query) {
  return request({
    url: '/model/RmCabinetModel/list',
    method: 'get',
    params: query
  })
}

// 查询机柜模板管理详细
export function getRmCabinetModel(id) {
  return request({
    url: '/model/RmCabinetModel/' + id,
    method: 'get'
  })
}

// 新增机柜模板管理
export function addRmCabinetModel(data) {
  return request({
    url: '/model/RmCabinetModel',
    method: 'post',
    data: data
  })
}

// 修改机柜模板管理
export function updateRmCabinetModel(data) {
  return request({
    url: '/model/RmCabinetModel',
    method: 'put',
    data: data
  })
}

// 删除机柜模板管理
export function delRmCabinetModel(id) {
  return request({
    url: '/model/RmCabinetModel/' + id,
    method: 'delete'
  })
}

// 选择查询机柜模板管理列表
export function listRmCabinetModels(query) {
  return request({
    url: '/model/RmCabinetModel/lists',
    method: 'get',
    params: query
  })
}
