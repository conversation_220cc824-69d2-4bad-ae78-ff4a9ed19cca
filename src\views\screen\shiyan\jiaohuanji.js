import * as THREE from 'three';
import {obj} from './share.js';
// 默认高度，但现在会从参数中获取实际高度
const defaultHeight = 3;
export function initjhj(startU, occupiedU, buttonCount, equipmentData) {
    return new Promise((resolve) => {
        const obj2 = {
            metalness: 0.1,  // 降低金属度，减少反光
            roughness: 0.5,  // 增加粗糙度，减少反光
            shininess: 1000,  // 降低高光指数
            x: 0,
            y: 0,
            z: 0,
        };

        // 确保buttonCount有效
        buttonCount = buttonCount || 48;

        // 解析参数
        // 使用传入的参数或默认值
        const height = defaultHeight; // 使用默认高度
        const U = height / buttonCount; // 使用实际的buttonCount

        // 创建一个几何体，高度根据占用U数动态计算
        let jhjbox;
        if (startU !== undefined && occupiedU !== undefined) {
            // 如果是连续区域的交换机，创建一个高度与连续区域大小相匹配的盒子
            jhjbox = new THREE.BoxGeometry(1, U * occupiedU, 1.5);
        } else {
            // 默认情况，创建标准大小的盒子
            jhjbox = new THREE.BoxGeometry(1, 0.2, 1.5);
        }

        const textureLoader = new THREE.TextureLoader();

        const loadTexture = (path) => {
            return new Promise((resolve) => {
                textureLoader.load(path, resolve);
            });
        };

        Promise.all([
            loadTexture(require("./pic/交换机前.png")),
            loadTexture(require("./pic/白色金属.png")),
            loadTexture(require('./pic/交换机左.png')),
            loadTexture(require('./pic/交换机右.png')),
        ]).then(([mapQ, map, mapZ, mapY]) => {
            const jhjmaterialQ = new THREE.MeshPhysicalMaterial({
                color: 0xffffff,
                map: mapQ,
                antialias: true,
                metalness: obj2.metalness,
                roughness: obj2.roughness,
                shininess: obj2.shininess,
            });

            const jhjmaterialZ = new THREE.MeshPhysicalMaterial({
                color: 0xffffff,
                map: mapZ,
                antialias: true,
                metalness: obj2.metalness,
                roughness: obj2.roughness,
                shininess: obj2.shininess,
            });

            const jhjmaterialY = new THREE.MeshPhysicalMaterial({
                color: 0xffffff,
                map: mapY,
                antialias: true,
                metalness: obj2.metalness,
                roughness: obj2.roughness,
                shininess: obj2.shininess,
            });

            const jhjmaterial = new THREE.MeshPhysicalMaterial({
                color: 0xffffff,
                map: map,
                antialias: true,
                metalness: obj2.metalness,
                roughness: obj2.roughness,
                shininess: obj2.shininess,
            });

            const materials = [
                jhjmaterialQ,
                jhjmaterial,
                jhjmaterial,
                jhjmaterial,
                jhjmaterialZ,
                jhjmaterialY,
            ];

            const group = new THREE.Group();
            const jhj = new THREE.Mesh(jhjbox, materials);
            jhj.rotation.y = -Math.PI / 2;
            group.add(jhj);

            // 处理交换机的位置和大小
            if (startU !== undefined && occupiedU !== undefined && buttonCount) {
                // 如果是连续区域的交换机
                // 使用与initjhj2相同的位置计算方式
                const centerY = -(height/2) + (startU * U) + (occupiedU * U / 2) - U;
                group.position.y = centerY;

                // 标记为交换机
                group.isJhj = true;
                group.name = 'jhj2';

                // 添加userData数据
                if (equipmentData) {
                    // 如果有传入设备数据，使用传入的数据
                    group.userData = {
                        switchData: {
                            ...equipmentData, // 包含所有设备数据
                            // 添加一些额外的3D相关数据
                            startU: startU,
                            occupiedU: occupiedU,
                            buttonCount: buttonCount,
                            // 确保有一些基本属性，如果设备数据中没有的话
                            type: equipmentData.equipmentType || '交换机',
                            model: equipmentData.model || ('JHJ-' + startU + '-' + occupiedU),
                            status: equipmentData.status || '正常',
                            manufacturer: equipmentData.manufacturer || '华为',
                            installDate: equipmentData.createTime || new Date().toISOString().split('T')[0]
                        }
                    };
                } else {
                    // 如果没有传入设备数据，使用默认数据
                    group.userData = {
                        switchData: {
                            startU: startU,
                            occupiedU: occupiedU,
                            buttonCount: buttonCount,
                            type: '交换机',
                            model: 'JHJ-' + startU + '-' + occupiedU,
                            status: '正常',
                            ports: occupiedU * 2, // 假设每U有2个端口
                            manufacturer: '华为',
                            installDate: new Date().toISOString().split('T')[0]
                        }
                    };
                }
            }

            resolve(group);
        });
    });
}

export function initjhj2(show3DPattern, startU, occupiedU, currentMouseNumber, buttonCount = 48, height = 3) {
    return new Promise((resolve) => {
        const obj2 = {
            metalness: 0.1,  // 降低金属度，减少反光
            roughness: 0.5,  // 增加粗糙度，减少反光
            shininess: 100,  // 降低高光指数
            x: 0,
            y: 0,
            z: 0,
        };

        // 确保buttonCount有效
        buttonCount = buttonCount || 48;

        // 计算U值 - 根据机柜实际高度和U数计算
        const U = height / buttonCount;

        // 创建一个简单的盒子几何体
        let jhjbox = null;

        if (startU && occupiedU) {
            // 如果是连续区域，创建一个高度与连续区域大小相匹配的盒子
            jhjbox = new THREE.BoxGeometry(1, U * occupiedU, 1.5);
        } else {
            // 如果是普通悬停，创建一个与单个U高度匹配的盒子
            // 使用U值而不是固定值，确保在不同机柜高度下显示正确
            jhjbox = new THREE.BoxGeometry(1, U, 1.5);
        }

        // 创建材质
        const jhjmaterial = new THREE.MeshPhysicalMaterial({
            color: startU && occupiedU ? 0x00ff00 : 0x02a1ec, // 如果是连续区域则使用绿色，否则使用蓝色
            antialias: true,
            metalness: obj2.metalness,
            roughness: obj2.roughness,
            shininess: obj2.shininess,
            opacity: 0.7,    // 透明度 0.7
            transparent: true, // 开启透明
            wireframe: false  // 非线框模式
        });

        // 创建网格对象
        const jhj = new THREE.Mesh(jhjbox, jhjmaterial);
        jhj.rotation.y = -Math.PI / 2;
        jhj.visible = show3DPattern;

        if (startU && occupiedU) {
            // 如果是连续区域，计算中心位置
            // 根据实际U数和高度计算位置
            const centerY = -(height/2) + (startU * U) + (occupiedU * U / 2) - U;
            jhj.position.y = centerY;
        } else if (currentMouseNumber !== undefined) {
            // 如果是普通悬停，使用当前鼠标位置
            // 根据实际U数和高度计算位置
            jhj.position.y = -(height/2) + U * (currentMouseNumber + 0.5);
        }

        resolve(jhj);
    });
}