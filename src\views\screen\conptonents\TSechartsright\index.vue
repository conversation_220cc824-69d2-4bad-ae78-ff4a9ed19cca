<template>
  <div class="content">
    <div class="left">
       <dv-decoration-12 style="width:150px;height:150px;">111</dv-decoration-12>
    </div>
    <div class="right">
      <div class="box" style="color:#3CE4C7;">
        <span>当前数量：</span>
        <span>101</span>
      </div>
      <div class="box" style="color:#3CE4C7;">
        <span>考核指标：</span>
        <span>10</span>
      </div>
      <div class="box" style="color:#3CE4C7;">
        <span>月度累计：</span>
        <span>10</span>
      </div>
      <div class="box" style="color:#3CE4C7;">
        <span>年度累计：</span>
        <span>10</span>
      </div>
    </div>
  </div>         
</template>
<script>
import * as echarts from "echarts";
export default {
  name: 'TSecharts',
  data(){
    return{
 
    }
  },
  created () {
  
  },
  methods: {
   
  }
}
</script>
<style lang="scss" scoped>
  .content{
    padding:20px;
    // padding-top:30px;
    text-align: center;
    color: #fff;
    align-items: center;
    display: flex;
    .left{
      width: 50%;
      padding-left:10%;
      display: flex;
      align-items: center;
      text-align: center;
      font-size: 30px;
    }
    .right{
      width: 50%;
      left:50%;
      text-align: left;
        .box{
          padding-left:20%;
          height: 30px;
        }
    }

  }
  </style>