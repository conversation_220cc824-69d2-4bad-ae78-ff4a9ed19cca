import * as THREE from 'three';
// import Stats from 'three/examples/jsm/libs/stats.module.js';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
// import {GLTFLoader }from 'three/examples/jsm/loaders/GLTFLoader.js';
// console.log("OrbitControls",OrbitControls)
// import { GUI } from 'three/examples/jsm/libs/lil-gui.module.min.js';
import { EffectComposer } from 'three/examples/jsm/postprocessing/EffectComposer.js';
import { RenderPass } from 'three/examples/jsm/postprocessing/RenderPass.js';
import { OutlinePass } from 'three/examples/jsm/postprocessing/OutlinePass.js';
// import { GlitchPass } from 'three/examples/jsm/postprocessing/GlitchPass.js';
import {GammaCorrectionShader} from 'three/examples/jsm/shaders/GammaCorrectionShader.js';
import {ShaderPass} from 'three/examples/jsm/postprocessing/ShaderPass.js';
// import { UnrealBloomPass } from 'three/examples/jsm/postprocessing/UnrealBloomPass.js';
// SMAA抗锯齿通道
import {SMAAPass} from 'three/examples/jsm/postprocessing/SMAAPass.js';
import TWEEN from '@tweenjs/tween.js';
// import { CSS3DRenderer, CSS3DObject } from 'three/examples/jsm/renderers/CSS3DRenderer.js';
// import { CSS2DRenderer, CSS2DObject } from 'three/examples/jsm/renderers/CSS2DRenderer.js';


import {initMotor} from './motor.js';
import {initWall} from './wall.js';
import jiaju from './jiaju.js';
import cabinets from './cabinets.js';
import { logger } from 'runjs/lib/common.js';
import { initMotor2 } from './motor2.js';



const scene = new THREE.Scene();
// scene.position.set(-7, 0, -10);

// scene.add(floor);
// scene.add(qiang);

// meshs.forEach(mesh=>{
//     scene.add(mesh);
// });
// scene.add(motor);

const model = new THREE.Group();
// model.add(motor);
// model.translateX(100);
// model.translateZ(100);
// scene.add(model);
scene.add(jiaju);
// scene.add(motor2);
// scene.add(jiaohuanji);

export function index(compDiv,updateSelectedCabinet,threeContainer,
    RmCabinetModelList,RmRoomList,miniContainer) {        

const qiang = initWall(RmRoomList);
scene.add(qiang);
const motor = new THREE.Group();
const jigui = initMotor(RmCabinetModelList);
jigui.name = 'jigui';
motor.add(jigui);
motor.name = 'motor';
scene.add(motor);
// const axesHelper = new THREE.AxesHelper( 1000 );
const ligth = new THREE.AmbientLight(0xffffff,0.5);
const ligth2 = new THREE.PointLight( 0xffffff, 5 );
const directionalLight = new THREE.DirectionalLight(0xffffff, 1); // 平行光
directionalLight.position.set(-5, 9, -5).normalize();
directionalLight.castShadow = true; // 允许投射阴影
scene.add(directionalLight);

// const ligthHelper = new THREE.PointLightHelper(ligth2,10);
// const ligthHelper = new THREE.DirectionalLightHelper(ligth, 10);
ligth.position.set( 300, 300, 300 );
ligth2.position.set(-4,10,-4);
scene.add(ligth2);

const victor = new THREE.Vector3( 0, 0, 0 );
ligth.target = victor;
scene.add(ligth);

const camera = new THREE.PerspectiveCamera( 75, window.innerWidth / window.innerHeight, 0.1, 5000 );
// camera.position.set( -RmRoomList[0].length/10, RmRoomList[0].height*2, RmRoomList[0].width);
// const victor2 = new THREE.Vector3( 100,0,100 );
// camera.lookAt(victor2);
scene.add( camera );

// scene.rotateX(Math.PI/2);

const renderer = new THREE.WebGLRenderer({alpha:true,antialias: true,
    powerPreference: 'high-performance', stencil: true,
});
renderer.setPixelRatio(window.devicePixelRatio);
// renderer.setClearColor(0x000ff6e,0);
renderer.setClearAlpha(0);
renderer.shadowMap.enabled = true; // 启用阴影
renderer.outputEncoding = THREE.sRGBEncoding;//GLTF编码
renderer.setSize( window.innerWidth*0.75, window.innerHeight );
threeContainer.appendChild( renderer.domElement );
// renderer.domElement.style.width = '100%';
// renderer.domElement.style.height = '100%';
// renderer.domElement.width = 1920;
// renderer.domElement.height = 1080;
const controls = new OrbitControls( camera, renderer.domElement );
controls.minPolarAngle = Math.PI *0 ; // 0 度
controls.maxPolarAngle = Math.PI *0.4; // 72 度
compDiv.style.display = 'none';
compDiv.style.opacity = '1';
camera.position.set(-5,RmRoomList[0].width*1.2,3);
// controls.target.set(RmRoomList[0].width/2, 0, RmRoomList[0].length/2); // 设置 OrbitControls 的目标点

// 创建一个CSS2渲染器CSS2D物体
// const compTag = new CSS3DObject(compDiv)
// scene.add(compTag);      //添加到场景中
// compTag.scale.set(0.5, 0.5, 0.5);
// compTag.position.set(200, 0,200);

// 创建 CSS2D 渲染器
// const cssRenderer = new CSS3DRenderer();
// cssRenderer.setSize(window.innerWidth, window.innerHeight);
// cssRenderer.domElement.style.position = 'absolute';
// cssRenderer.domElement.style.top = 0;
// cssRenderer.domElement.style.left = 0;
// cssRenderer.domElement.style.zIndex = '1'; // 确保在 WebGL 渲染器之上
// cssRenderer.domElement.style.pointerEvents = 'none'; // 确保鼠标事件穿透
// document.body.appendChild(cssRenderer.domElement);

const composer = new EffectComposer(renderer);
const renderPass = new RenderPass(scene, camera);
composer.addPass(renderPass);
const v2 = new THREE.Vector2(window.innerWidth, window.innerHeight);
const outlinePass = new OutlinePass(v2, scene, camera);
outlinePass.visibleEdgeColor.set(0x3e67f9);
// console.log('描边厚度',outlinePass.edgeThickness);          
outlinePass.edgeThickness = 4.0;
// console.log('描边亮度',outlinePass.edgeStrength);
outlinePass.edgeStrength = 10; 
outlinePass.pulsePeriod = 2;
composer.addPass(outlinePass);  //添加指定物体描边

// const bloomPass = new UnrealBloomPass(new THREE.Vector2(window.innerWidth, window.innerHeight));
// bloomPass.strength = 0.3;
// composer.addPass(bloomPass);
// const glitchPass = new GlitchPass();
// glitchPass.enabled = true;
// composer.addPass(glitchPass);
// 创建伽马校正通道
const gammaPass= new ShaderPass(GammaCorrectionShader);
composer.addPass(gammaPass);
// const pixelRatio = renderer.getPixelRatio();
// // width、height是canva画布的宽高度，抗锯齿后处理
const smaaPass = new SMAAPass(window.innerWidth, Window.innerHeight);
composer.addPass(smaaPass);
addEventListener('click',async function(event){

  const width = this.window.innerWidth*0.75;
  const height = this.window.innerHeight;
//   if (width < 600) {
//     // compDiv.textContent = 'Small Screen';
//     compDiv.style.height = '210px';
//     compDiv.style.width = '120px';
// } else if (width >= 600 && width < 1000) {
//     // compDiv.textContent = 'Medium Screen';
//     compDiv.style.height = '350px';
//     compDiv.style.width = '200px';
// }else {
//     compDiv.style.height = '600px';
//     compDiv.style.width = '400px';
// }
  // .offsetY、.offsetX以canvas画布左上角为坐标原点,单位px
  const px = event.offsetX;
  const py = event.offsetY;
  //屏幕坐标px、py转WebGL标准设备坐标x、y
  //width、height表示canvas画布宽高度
  const x = (px / width) * 2 - 1;
  const y = -(py / height) * 2 + 1;
  console.log(x,y);
  //创建一个射线投射器`Raycaster`
  const raycaster = new THREE.Raycaster();
  //.setFromCamera()计算射线投射器`Raycaster`的射线属性.ray
  // 形象点说就是在点击位置创建一条射线，射线穿过的模型代表选中
  raycaster.setFromCamera(new THREE.Vector2(x, y), camera);
  //.intersectObjects([mesh1, mesh2, mesh3])对参数中的网格模型对象进行射线交叉计算
  // 未选中对象返回空数组[],选中一个对象，数组1个元素，选中两个对象，数组两个元素
  const intersects = raycaster.intersectObjects([motor]);
  if (intersects.length ===0) {
    console.log("No intersection found");
    compDiv.style.display = 'none';
    updateSelectedCabinet(null);
    return;
}
const selectedObject = intersects[0].object;
console.log("射线器返回的对象", intersects[0].object);
const index = outlinePass.selectedObjects.indexOf(intersects[0].object);
//   const selectedObject = intersects[0].object;
//   selectedObject2 = selectedObject;
  // intersects.length等于-1说明，说明选中了模型
  const tween = new TWEEN.Tween(selectedObject.position);
  if (index === -1) {
    outlinePass.selectedObjects.forEach(obj => {
        if (obj !== selectedObject) {
            new TWEEN.Tween(obj.position)
                .to({ y: 11 }, 500) // 500ms 内恢复原始位置
                .easing(TWEEN.Easing.Quadratic.Out)
                .onComplete(() => {
                    delete obj.userData.originalPosition;
                })
                .start();
        }
    });
    outlinePass.selectedObjects = [];
    outlinePass.selectedObjects.push(selectedObject);
    tween
    .to({y: 30}, 500)
    .easing(TWEEN.Easing.Quadratic.Out)
    .onComplete(() => {
        delete selectedObject.userData.originalPosition;
    })
    .start();
    compDiv.style.display = 'block';
} else {
    outlinePass.selectedObjects.splice(index, 1);
    tween
    .to({ y: 11 }, 500) // 500ms 内恢复原始位置
    .easing(TWEEN.Easing.Quadratic.Out)
    .onComplete(() => {
        delete selectedObject.userData.originalPosition;
    })
    .start();
    compDiv.style.display = 'none';
}


// Update selected cabinet data
if (selectedObject && selectedObject.userData.cabinetData) {
    const cabinetData = selectedObject.userData.cabinetData;
    updateSelectedCabinet(cabinetData);
} else {
    updateSelectedCabinet(null);
}
if(index===-1){
    this.motor2 = initMotor2(selectedObject.userData.cabinetData);
    this.motor2.then((group) => {
        creatMiniContainer(group,miniContainer);
    });
    this.motor2.name = 'motor2';

}             


})

function animate() {
    requestAnimationFrame(animate);
    // 注释掉这行，避免在动画循环中覆盖基于机房数据设置的旋转中心
    // updateControlsTarget(motor,controls) ;

    TWEEN.update();
    // 更新 OrbitControls
    controls.update();
    const lightTarget = new THREE.Vector3();
    camera.getWorldDirection(lightTarget);
    directionalLight.position.copy(camera.position);
    directionalLight.target.position.copy(camera.position).add(lightTarget);
    directionalLight.target.updateMatrixWorld();
    // compRenderer.render(scene, camera);
    // 执行渲染操作
    // renderer.render(scene, camera);
    // cssRenderer.render(scene, camera);
    composer.render();
    // css3Renderer.render(scene, camera);
    // labelRenderer.render(scene, camera);
    // console.log('camera.position=====',camera.position);
    // console.log('controls.target=====',controls.target);
}
animate();

// renderer.rotateX(Math.PI/2);
// camera.position.set( 0, 20, 100 );
// controls.update();
// controls.update();
window.onresize = function () {
    composer.setSize(window.innerWidth, window.innerHeight);
    // 重置渲染器输出画布canvas尺寸
    // renderer.setSize(window.innerWidth, window.innerHeight);
    // 全屏情况下：设置观察范围长宽比aspect为窗口宽高比
    camera.aspect = window.innerWidth / window.innerHeight;
    // 渲染器执行render方法的时候会读取相机对象的投影矩阵属性projectionMatrix
    // 但是不会每渲染一帧，就通过相机的属性计算投影矩阵(节约计算资源)
    // 如果相机的一些属性发生了变化，需要执行updateProjectionMatrix ()方法更新相机的投影矩阵
    camera.updateProjectionMatrix();
    // css3Renderer.setSize(width,height);

};
scene.background = null;
return scene;
}
function updateControlsTarget(motor,controls) {
    const box = new THREE.Box3().setFromObject(motor);
    const center = new THREE.Vector3();
    box.getCenter(center);
    controls.target.set(center.x, center.y-3, center.z);
    controls.update();
  }

export function creatMiniContainer(motor2,miniContainer){
    const existingCanvas = miniContainer.querySelector('canvas');
    if (existingCanvas) {// 移除旧的 <canvas> 元素
        miniContainer.removeChild(existingCanvas);
    }
        const scene1 = new THREE.Scene();
        scene1.add(motor2);
        const camera1 = new THREE.PerspectiveCamera(75, 350/400, 0.1, 1000);
        camera1.position.z = 1.5;

        const light = new THREE.AmbientLight(0xffffff, 5);
        scene1.add(light);
  
        const renderer = new THREE.WebGLRenderer({alpha:true,antialias: true,});
        renderer.domElement.style.width = '350px';
        renderer.domElement.style.height = '400px';
        renderer.setSize(350, 400); // 实际像素分辨率
        renderer.setPixelRatio(window.devicePixelRatio);
        // renderer.setClearColor(0x000ff6e,0);
        renderer.setClearAlpha(0);

        // const axesHelper = new THREE.AxesHelper( 50 );
        renderer.outputEncoding = THREE.sRGBEncoding;//GLTF编码
        // console.log('this.$refs.miniContainer.clientWidth',this.$refs.miniContainer.clientWidth);
        miniContainer.appendChild(renderer.domElement);
        
        // scene1.add(axesHelper);
        renderer.render(scene1, camera1);
        const controls = new OrbitControls(camera1, renderer.domElement);
        controls.addEventListener('change', function () {
            renderer.render(scene1, camera1); //执行渲染操作
        });//监听鼠标、键盘事件
        return scene1;
    }
    // 调整模型自身旋转中心
function changeModelCenter(x, y, z, obj) {
    const wrapper = new THREE.Object3D(); // 存放目标模型
    wrapper.position.set(x, y, z);
    obj.position.set(-x, -y, -z);
    wrapper.add(obj);
    return wrapper;
  }