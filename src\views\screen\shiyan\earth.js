import * as THREE from 'three';

const texture = new THREE.TextureLoader().load('./pic/earth.jpg');
const material = new THREE.MeshBasicMaterial({
	map: texture
});

const geometry = new THREE.SphereGeometry(5,128,128);

// const geometry = new THREE.BufferGeometry(); 
// const vertices = new Float32Array([
//     0, 0, 0, //顶点1坐标
//     16, 0, 0, //顶点2坐标
//     16, 8, 0, //顶点3坐标
//     0, 8, 0, //顶点4坐标
// ]);
// geometry.attributes.position = new THREE.BufferAttribute(vertices, 3);//各个点位置
// const indexs = new Uint16Array([
//     0,1,2,0,2,3
// ]);
// const index = new THREE.BufferAttribute(indexs, 1); 
// geometry.setIndex(index);//设置点索引
// geometry.attributes.uv = new THREE.BufferAttribute(new Float32Array([
//     1, 1,
//     0, 1,
//     0, 0,
//     1, 0,
// ]), 2);
const uvAttribute = geometry.attributes.uv;
console.log("uv",uvAttribute);
for (let i = 0; i < uvAttribute.count; i++) {
    const v = uvAttribute.array[i * 2 + 1];
    uvAttribute.array[i * 2 + 1] = 1 - v;
}
const earth = new THREE.Mesh(geometry, material);

export default earth;