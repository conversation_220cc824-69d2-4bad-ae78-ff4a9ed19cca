<template>
    <div class="banka-container" ref="bankaContainer">
      <!-- 板卡展示区域 - 使用HTML/CSS实现 -->
      <div class="banka-display" ref="bankaDisplay">
        <!-- 板卡列表 - 仅在有交换机数据时显示 -->
        <div v-if="switchData && switchData.userData && switchData.userData.switchData && switchData.userData.switchData.rmEquipmentSlotList" class="banka-list">
          <div
            v-for="(rmEquipmentSlot, index) in switchData.userData.switchData.rmEquipmentSlotList"
            :key="rmEquipmentSlot.uuid"
            class="banka-card"
          >
            <!-- 板卡图片 -->
            <div class="banka-image">
              <!-- 板卡编号 -->
              <div class="banka-number">{{ index + 1 }}</div>

              <!-- 端口区域 -->
              <div class="ports-container">
                <!-- 左侧端口 -->
                <div class="ports">
                  <div class="port"></div>
                  <div class="port"></div>
                  <div class="port"></div>
                  <div class="port"></div>
                </div>
              </div>
            </div>
            <!-- 板卡信息 -->
            <div class="banka-info">
              <div class="banka-name">{{ rmEquipmentSlot.slotNumber+'号槽位' || '板卡 ' + (index + 1) }}</div>
              <div class="banka-status" :class="{'status-active': rmEquipmentSlot.status === '1'}">
                {{ rmEquipmentSlot.status === '1' ? '已使用' : '未使用' }}
              </div>
            </div>
          </div>

          <!-- 如果没有板卡数据，显示提示信息 -->
          <div v-if="switchData.userData.switchData.rmEquipmentSlotList.length === 0" class="no-data">
            <i class="el-icon-info"></i>
            <span>暂无板卡数据</span>
          </div>
        </div>
      </div>

      <!-- 交换机数据展示区域 -->
      <!-- <div v-if="switchData" class="switch-data">
        <h3>交换机数据</h3>
        <pre>{{ JSON.stringify(switchData, null, 2) }}</pre>
      </div> -->
    </div>
  </template>

  <script>
  import { EventBus } from '@/utils/eventBus';
  import { getRmEquipment } from "@/api/equipment/RmEquipment";

  export default {
    name: 'Banka',
    data() {
      return {
        switchData: null, // 存储交换机数据
      };
    },
    mounted() {
      this.initEventListeners();
    },
    methods: {
      // 初始化事件监听器
      initEventListeners() {
        // 监听交换机点击事件
        EventBus.$on('switch-clicked', (data) => {
          
          // 保存初始数据
          this.switchData = data;

          // 如果userData中有交换机数据且有uuid，则通过API获取详细数据
          if (data.userData && data.userData.switchData && data.userData.switchData.uuid) {
            this.fetchSwitchData(data.userData.switchData.uuid);
          }

          // 通过根实例发送事件，通知 Editor.vue 显示板卡组件
          this.$root.$emit('switch-clicked');
        });

        // 监听交换机清除事件
        EventBus.$on('switch-cleared', () => {
          this.switchData = null;

          // 通过根实例发送事件，通知 Editor.vue 隐藏板卡组件
          this.$root.$emit('switch-cleared');
        });
      },

      // 获取交换机详细数据
      fetchSwitchData(uuid) {
        getRmEquipment(uuid).then(response => {
          console.log('获取到交换机详细数据:', response.data);

          // 更新switchData，将API返回的数据合并到原有数据中
          if (this.switchData && this.switchData.userData) {
            // 将API返回的数据存入userData
            this.switchData.userData.switchData = {
              ...this.switchData.userData.switchData,
              ...response.data
            };
          }
        }).catch(error => {
          console.error('获取交换机数据失败:', error);
        });
      }
    },
    beforeDestroy() {
      // 移除事件监听器
      EventBus.$off('switch-clicked');
      EventBus.$off('switch-cleared');
    }
  };
  </script>

  <style scoped>
  .banka-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    /* overflow: hidden; */
    padding-top: 20px;
    border-radius: 5px;
    background-color: transparent;
  }

  .banka-display {
    width: 100%;
    height: auto;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    margin-bottom: 20px;
  }

  /* 板卡列表样式 */
  .banka-list {
    width: 95%;
    display: grid;
    grid-template-columns: repeat(2, 1fr); /* 一行放两个 */
    grid-gap: 15px;
    padding: 10px;
    max-height: 500px;
    /* overflow-y: auto; */
    justify-items: center;
  }

  /* 板卡卡片样式 */
  .banka-card {
    width: 120px;
    height: 200px;
    /* background-color: rgba(245, 245, 245, 0.2); */
    border: 1px solid rgba(204, 204, 204, 0.3);
    border-radius: 3px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    backdrop-filter: blur(2px);
  }

  .banka-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.15);
    /* background-color: rgba(245, 245, 245, 0.3); */
  }

  /* 板卡图片区域 */
  .banka-image {
    height: 150px;
    background-color: transparent;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    position: relative;
    border-bottom: 1px solid rgba(204, 204, 204, 0.3);
    padding: 10px 0;
  }

  /* 端口容器样式 */
  .ports-container {
    display: flex;
    justify-content: center;
    width: 100%;
    padding: 0 5px;
    margin-top: 10px;
  }

  /* 左侧端口组 */
  .ports {
    display: flex;
    flex-direction: column;
    justify-items: center;
    gap: 8px;
  }

  /* 右侧端口组 */
  .ports-right {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  /* 单个端口样式 */
  .port {
    width: 100px;
    height: 18px;
    background-color: rgba(51, 51, 51, 0.7);
    border-radius: 1px;
    border: 1px solid rgba(85, 85, 85, 0.5);
  }

  /* 板卡编号 */
  .banka-number {
    font-size: 18px;
    font-weight: bold;
    color: rgba(255, 255, 255, 0.9);
    margin-top: 5px;
    background-color: rgba(39, 110, 118, 0.6);
    padding: 2px 8px;
    border-radius: 10px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  /* 板卡信息区域 */
  .banka-info {
    padding: 6px;
    display: flex;
    flex-direction: column;
    align-items: center;
    /* background-color: rgba(255, 255, 255, 0.1); */
    height: 50px;
    backdrop-filter: blur(2px);
  }

  .banka-name {
    font-size: 12px;
    font-weight: bold;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 5px;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
  }

  .banka-status {
    font-size: 10px;
    color: rgba(255, 255, 255, 0.7);
    padding: 1px 6px;
    border-radius: 8px;
    background-color: rgba(240, 240, 240, 0.2);
  }

  .status-active {
    color: rgba(255, 255, 255, 0.9);
    background-color: rgba(103, 194, 58, 0.6);
  }

  /* 无数据提示 */
  .no-data {
    width: 100%;
    height: 100px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: rgba(255, 255, 255, 0.7);
    grid-column: 1 / -1; /* 横跨所有列 */
  }

  .no-data i {
    font-size: 24px;
    margin-bottom: 10px;
  }

  /* 交换机数据区域 */
.switch-data {
  width: 90%;
  height: 60%;
  /* max-height: calc(100% - 250px); */
  padding: 10px;
  margin: 0 auto;
  border: 1px solid rgba(221, 221, 221, 0.3);
  /* overflow: auto; */
  color: rgba(255, 255, 255, 0.9);
  border-radius: 5px;
  background-color: transparent; /* 关键：设置为透明背景 */
}

  .switch-data h3 {
    margin-top: 0;
    margin-bottom: 10px;
    color: rgba(39, 110, 118, 0.9);
    font-size: 16px;
    text-align: center;
    border-bottom: 1px solid rgba(238, 238, 238, 0.2);
    padding-bottom: 5px;
  }

  .switch-data pre {
    margin: 0;
    font-family: monospace;
    font-size: 14px;
    white-space: pre-wrap;
    word-break: break-all;
    color: rgba(255, 255, 255, 0.9);
    padding: 10px;
    border-radius: 4px;
    max-height: 300px;
    overflow-y: auto;
  }
  </style>