<template>
  <div>
    <el-dialog :title="title" :visible.sync="dialogFormVisible" width="50%" height="50vh" append-to-body
      :close-on-click-modal="false">
      <template slot="title">
        <div class="avue-crud__dialog__header" style="border-bottom:1px solid #DCDFE6;padding-bottom:15px;">
          <span class="el-dialog__title">
            <span
              style="display:inline-block;background-color: #3478f5;width:3px;height:20px;margin-right:5px; float: left;margin-top:2px;margin-right:15px"></span>
            {{ title }}
          </span>
        </div>
      </template>
      <el-row :gutter="20">
        <!--部门数据-->
        <el-col :span="8" :xs="24">
          <div class="head-container">
            <el-input v-model="deptName" placeholder="请输入部门名称" clearable size="small" prefix-icon="el-icon-search"
              style="margin-bottom: 20px" />
          </div>
          <div class="head-container app-depets">
            <el-tree :data="deptOptions" :props="defaultProps" :expand-on-click-node="false"
              :filter-node-method="filterNode" ref="tree" highlight-current @node-click="handleNodeClick" />
          </div>
        </el-col>
        <!--用户数据-->
        <el-col :span="16" :xs="24">
          <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
            label-width="68px">
            <el-form-item label="姓名" prop="nickName">
              <el-input v-model="queryParams.nickName" placeholder="请输入姓名" clearable style="width: 220px"
                @keyup.enter.native="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
          <el-row :gutter="10" class="mb8">
            <!-- <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          :disabled="multiple"
          @click="submitForm"
        >选择</el-button>
      </el-col> -->
            <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
          </el-row>
          <el-table :data="userList" @selection-change="handleSelectionChange" stripe border height="60vh"
            @row-dblclick="handleTableRow">
            <!-- <el-table-column type="selection" width="50" align="center" /> -->
            <el-table-column prop="" label="序号" width="50" align="center" type="index"
              :index="indexMethod"></el-table-column>
            <el-table-column label="部门" align="center" key="deptName" prop="dept.deptName" />
            <el-table-column label="姓名" align="center" key="nickName" prop="nickName" />
          </el-table>
        </el-col>
      </el-row>
      <!-- <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">选 择</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div> -->
    </el-dialog>
  </div>
</template>

<script>
import { getlistUser, deptTreeSelect } from "@/api/system/user";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  name: "User",
  dicts: ["sys_normal_disable", "sys_user_sex"],
  components: { Treeselect },
  data() {
    return {
      dialogFormVisible: false,
      // 遮罩层
      loading: true,
      ids: {
        username: [],
        userid: []
      },
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 用户表格数据
      userList: null,
      // 弹出层标题
      title: "选择参会人员",
      // 部门树选项
      deptOptions: undefined,
      // 部门名称
      deptName: undefined,
      // 日期范围
      dateRange: [],
      // 岗位选项
      postOptions: [],
      // 角色选项
      roleOptions: [],
      // 表单参数
      form: {},
      defaultProps: {
        children: "children",
        label: "label"
      },
      // 查询参数
      queryParams: {
        // pageNum: 1,
        // pageSize: 10,
        deptId: undefined,
        nickName: undefined
      },
      res: {
        userId: [],
        nickName: []
      },
      type: "",
    };
  },
  watch: {
    // 根据名称筛选部门树
    deptName(val) {
      this.$refs.tree.filter(val);
    }
  },
  created() {
    this.getDeptTree();
  },
  methods: {
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      getlistUser(this.queryParams).then(response => {
        this.userList = response.rows;
        this.loading = false;
      });
    },
    /** 查询部门下拉树结构 */
    getDeptTree() {
      deptTreeSelect().then(response => {
        this.deptOptions = response.data;
      });
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    // 节点单击事件
    handleNodeClick(data) {
      this.queryParams.deptId = data.id;
      this.handleQuery();
    },

    // 取消按钮
    cancel() {
      this.dialogFormVisible = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        userId: undefined,
        deptId: undefined,
        userName: undefined,
        nickName: undefined,
        password: undefined,
        phonenumber: undefined,
        email: undefined,
        sex: undefined,
        status: "0",
        remark: undefined,
        postIds: [],
        roleIds: []
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids.userid = selection.map(item => item.userId);
      this.ids.username = selection.map(item => item.userName);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },

    /** 提交按钮 */
    submitForm(row) {
      this.$emit("buttonEvent", row);
      // console.log("row==",row);
      this.dialogFormVisible = false;
    },
    handleTableRow(row, event, column) {
      // this.handleView(row);
      // console.log("row22==", row);
      // console.log("row==",row);
      this.$emit("buttonEvent", row);
      // switch (this.type) {
      //   case 1:
      //     this.$emit("buttonEvent", row);
      //     break;
      //   case 2:
      //     this.$emit("buttonEvents", row);
      //     break;
      //   case 3:
      //     this.$emit("buttonEvents1", row);
      //     break;
      //   case 4:
      //     this.$emit("buttonEvents2", row);
      //     break;
      //   default:
      //     console.log("无需任何操作")
      // }
      // if(this.type==1){
      //   this.$emit("buttonEvent", row);
      // }else if(this.type==2){
      //   console.log("row33==",row);
      //   this.$emit("buttonEvents", row);
      // }
      this.dialogFormVisible = false;
    },
    //序号
    indexMethod(index) {
      return index + 1;
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      // console.log(row);
      const ids = row.nickName || this.ids;
      // console.log(ids);
    }
  }
};
</script>
<style>
.treeselect-main {
  width: 204px;
}

.app-depets {
  max-height: 60vh;
  overflow: auto;
}
</style>