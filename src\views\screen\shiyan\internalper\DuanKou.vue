<template>
<transition name="el-fade-in-linear">
  <div class="duankou-container" v-if="visible" @click.stop>
      <el-card class="duankou-card" shadow="always">
        <div slot="header" class="duankou-header">
          <span class="header-title">端口信息</span>
          <el-button type="text" class="close-btn" @click="closePanel">
            <i class="el-icon-close"></i>
          </el-button>
        </div>

        <div class="duankou-body">
          <!-- 基本信息部分 -->
          <el-divider content-position="left">基本信息</el-divider>
          <el-form ref="form" :model="portData" label-width="100px" size="small">
            <el-form-item label="端口编号：" v-if="portData">
              <el-tag size="medium">{{ portData.portNumber || '未知' }}</el-tag>
            </el-form-item>
            <el-form-item label="端口类型：" v-if="portData">
              <el-tag>{{ portData.portType || '未知' }}</el-tag>
            </el-form-item>
            <el-form-item label="位置：" v-if="portData">
              <el-tag type="info" size="small">X: {{ portData.positionX || '0' }}</el-tag>
              <el-tag type="info" size="small" style="margin-left: 5px;">Y: {{ portData.positionY || '0' }}</el-tag>
            </el-form-item>
            <el-form-item label="状态：" v-if="portData">
              <el-tag :type="portData.status === '0' ? 'success' : 'danger'" size="small">
                {{ portData.status === '1' ? '停用' : '正常' }}
              </el-tag>
            </el-form-item>
            <el-form-item label="备注：" v-if="portData && portData.remarks">
              <span>{{ portData.remarks || '无' }}</span>
            </el-form-item>
            <el-form-item label="所属板卡：" v-if="portData && portData.boardName">
              <el-tag type="primary" size="small">{{ portData.boardName || '无' }}</el-tag>
            </el-form-item>
          </el-form>

          <!-- 端口连接信息部分 -->
        </div>
      </el-card>
  </div>
</transition>
</template>

<script>
import { EventBus } from '@/utils/eventBus';

export default {
  name: 'DuanKou',
  data() {
    return {
      visible: false,
      portData: null
    };
  },
  mounted() {
    this.initEventListeners();
  },
  methods: {
    initEventListeners() {
      // 监听端口点击事件
      EventBus.$on('port-clicked', (data) => {
        // console.log('接收到点击端口数据:', data);

        // 显示面板
        this.visible = true;

        // 设置端口数据
        this.portData = data;

        // 模拟连接信息（实际项目中应该从API获取）
        // 这里只是为了演示，实际应用中应该从后端获取真实数据
        // if (this.portData) {
        //   // 随机生成一些连接信息用于演示
        //   if (Math.random() > 0.5) {
        //     this.portData.connectionInfo = {
        //       deviceName: '设备-' + Math.floor(Math.random() * 100),
        //       portName: '端口-' + Math.floor(Math.random() * 10),
        //       status: Math.random() > 0.3 ? '0' : '1'
        //     };
        //   } else {
        //     this.portData.connectionInfo = null;
        //   }
        // }
      });

      // 监听端口清除事件
      EventBus.$on('port-cleared', () => {
        this.visible = false;
        this.portData = null;
      });

      // 监听槽位清除事件（当槽位面板关闭时，也关闭端口面板）
      EventBus.$on('slot-cleared', () => {
        this.visible = false;
        this.portData = null;
      });
    },

    // 关闭面板
    closePanel() {
      this.visible = false;
      // 发送清除事件
      EventBus.$emit('port-cleared');
    }
  },
  beforeDestroy() {
    // 移除事件监听器
    EventBus.$off('port-clicked');
    EventBus.$off('port-cleared');
    EventBus.$off('slot-cleared');
  }
};
</script>

<style scoped>
.duankou-container {
  height: 100%;
  pointer-events: auto;
}

.duankou-content {
  height: 100%;
  width: 300px;
  transform: translateX(100%);
  transition: transform 0.3s ease-in-out;
}

.slide-in {
  transform: translateX(0);
}

.duankou-card {
  height: 100%;
  border-radius: 0;
  border-left: 1px solid rgba(153, 102, 204, 0.3);
  background-color: rgba(31, 0, 62, 0.9);
}

.duankou-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: rgba(41, 0, 82, 0.9);
  border-bottom: 0px;
}

.header-title {
  color: #9966cc;
  font-size: 16px;
  font-weight: bold;
}

.close-btn {
  color: #9966cc;
}

.duankou-body {
  padding: 15px;
  overflow-y: auto;
  max-height: calc(100% - 60px);
}

.el-divider {
  background-color: rgba(153, 102, 204, 0.3);
}

.el-divider__text {
  color: #9966cc;
  font-weight: bold;
  background-color: rgba(31, 0, 62, 0.9);
}

.el-form-item {
  margin-bottom: 15px;
}

.el-form-item__label {
  color: rgba(255, 255, 255, 0.7);
}
</style>