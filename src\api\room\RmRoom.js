import request from '@/utils/request'

// 查询机房管理列表
export function listRmRoom(query) {
  return request({
    url: '/room/RmRoom/list',
    method: 'get',
    params: query
  })
}

// 查询机房管理详细
export function getRmRoom(id) {
  return request({
    url: '/room/RmRoom/' + id,
    method: 'get'
  })
}

// 新增机房管理
export function addRmRoom(data) {
  return request({
    url: '/room/RmRoom',
    method: 'post',
    data: data
  })
}

// 修改机房管理
export function updateRmRoom(data) {
  return request({
    url: '/room/RmRoom',
    method: 'put',
    data: data
  })
}

// 删除机房管理
export function delRmRoom(id) {
  return request({
    url: '/room/RmRoom/' + id,
    method: 'delete'
  })
}

// 查询机房管理列表
export function listRmRooms(query) {
  return request({
    url: '/room/RmRoom/lists',
    method: 'get',
    params: query
  })
}
