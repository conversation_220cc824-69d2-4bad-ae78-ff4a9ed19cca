<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <style> 
    /* .container {
        width: 1902px;
        height: 935px;
        overflow: hidden;
        background: black;
        display: inline-block;
    } */
    .card {
    border: red 5px solid;
        height: 700px;
        width: 400px;
        position: absolute;
        display: inline-block;
        color: white;
        background: rgb(65, 92, 244);
        opacity: 0; /* 初始透明度为0 */
        transition: opacity 1.5s ease-in-out; /* 定义过渡效果 */
    }
    </style>
    <!-- <style>
        body {
            overflow: hidden;
            margin: 0px;
        }

        .bu {
            border: 2px solid red; 
            background: rgba(255, 255, 255, 0.1);
            width: 60px;
            height: 60px;
            line-height: 60px;
            text-align: center;
            color: #fff;
            display: inline-block;
            border-radius: 30px;
        }

        .bu:hover {
            cursor: pointer;
        }

        .pos {
            border:2px soild red;
            position: absolute;
            left: 50%;
            margin-left: -65px;
            bottom: 100px;
        }
        #container {
            position: absolute;
            width: 400px;
            height: 16px;
            top: 50%;
            left:50%;
            margin-left: -200px;
            margin-top: -8px;
            border-radius: 8px;           
            border: 1px solid #009999;          
            overflow: hidden;
        }
        #per {
            height: 100%;
            width: 0px;
            background: #00ffff;
            color: #00ffff;
            line-height: 15px;          
        }
    </style> -->
</head>
<div class="card" id="cardId" type="module"></div>
    <!-- <canvas id="canvas"></canvas> -->
<body>
    <!-- <div id="container"> -->
        <!-- 进度条 -->
        <!-- <div id="per"> </div> -->
    <!-- </div> -->
    <!-- <script>         -->
        <!-- const percentDiv = document.getElementById("per");// 获取进度条元素 -->
        <!-- percentDiv.style.width = 0.8*400 + "px";//进度条元素长度 -->
        <!-- percentDiv.style.textIndent = 0.8*400 + 5 +"px";//缩进元素中的首行文本 -->
        <!-- percentDiv.innerHTML =  "80%";//进度百分比 -->
    <!-- </script> -->


    <!-- <div class="pos" style="border: 2px solid red;">
        <div  id="red" class="bu" style="border: 2px solid red;">红</div>
        <div  id="green" class="bu" style="margin-left: 10px;">绿</div>
    </div> -->
    <!-- type="importmap"功能：.html文件中也能和nodejs开发环境中一样方式，引入npm安装的js库 -->
    <script type="importmap">
        {
            "imports": {
                "three": "../three.js-r171/build/three.module.js",
                "three/examples/jsm/": "../three.js-r171/examples/jsm/",
                "@tweenjs/tween.js": "../node_modules/@tweenjs/tween.js/dist/tween.esm.js"
            }
        }
	</script>
    <!-- <script src="./index3.js" type="module" ></script> -->
    <script src="./index3.js" type="module" ></script>
    <!-- <div id="tag">标签内容</div>
    <div style="height: 300px;background: #999;">默认定位元素</div>
    <div style="position: absolute;">绝对定位</div> -->
    <!-- <div class="container" ref="container"></div> -->

        <!-- <canvas id="canvas2"></canvas> -->
        <!-- 模型标签<button id = 'anniu'>按钮</button> -->
    
    

</body>

<style>
    
</style>
</html>