<template>
  <div class="app-container">
    <el-dialog :title="title" :visible.sync="dialogFormVisible" width="50%" height="50vh" append-to-body
      :close-on-click-modal="false">
      <template slot="title">
        <div class="avue-crud__dialog__header" style="border-bottom:1px solid #DCDFE6;padding-bottom:15px;">
          <span class="el-dialog__title">
            <span
              style="display:inline-block;background-color: #3478f5;width:3px;height:20px;margin-right:5px; float: left;margin-top:2px;margin-right:15px"></span>
            {{ title }}
          </span>
        </div>
      </template>
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="120px">
        <el-form-item label="所属机房" prop="roomName">
          <el-input v-model="queryParams.roomName" placeholder="请输入所属机房" clearable @keyup.enter.native="handleQuery"
            style="width:240px" />
        </el-form-item>
        <el-form-item label="设备名称" prop="equipmentName">
          <el-input v-model="queryParams.equipmentName" placeholder="请输入设备名称" clearable
            @keyup.enter.native="handleQuery" style="width:240px" />
        </el-form-item>
        <el-form-item label="设备编码" prop="equipmentCode">
          <el-input v-model="queryParams.equipmentCode" placeholder="请输入设备编码" clearable
            @keyup.enter.native="handleQuery" style="width:240px" />
        </el-form-item>
        <el-form-item label="设备类型" prop="equipmentType">
          <el-select v-model="queryParams.equipmentType" placeholder="请选择设备类型" clearable style="width:240px">
            <el-option v-for="dict in dict.type.equipment_type" :key="dict.value" :label="dict.label"
              :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="设备型号" prop="model">
          <el-input v-model="queryParams.model" placeholder="请输入设备型号" clearable @keyup.enter.native="handleQuery"
            style="width:240px" />
        </el-form-item>
        <el-form-item label="设备品牌" prop="brand">
          <el-input v-model="queryParams.brand" placeholder="请输入设备品牌" clearable @keyup.enter.native="handleQuery"
            style="width:240px" />
        </el-form-item>
        <el-form-item label="设备IP地址" prop="ipAddress">
          <el-input v-model="queryParams.ipAddress" placeholder="请输入设备IP地址" clearable @keyup.enter.native="handleQuery"
            style="width:240px" />
        </el-form-item>
        <el-form-item label="设备状态" prop="status">
          <el-select v-model="queryParams.status" placeholder="请选择设备状态" clearable style="width:240px">
            <el-option v-for="dict in dict.type.equipment_status" :key="dict.value" :label="dict.label"
              :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="RmEquipmentList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column prop="" label="序号" width="80" align="center" type="index" :index="indexMethod" />
        <el-table-column label="所属区域" align="center" prop="deptId" />
        <el-table-column label="所属机房" align="center" prop="roomName" />
        <el-table-column label="所属机柜" align="center" prop="cabinetName" />
        <el-table-column label="设备名称" align="center" prop="equipmentName" />
        <el-table-column label="设备编码" align="center" prop="equipmentCode" />
        <el-table-column label="设备类型" align="center" prop="equipmentType">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.equipment_type" :value="scope.row.equipmentType" />
          </template>
        </el-table-column>
        <el-table-column label="设备型号" align="center" prop="model" />
        <el-table-column label="设备 IP 地址" align="center" prop="ipAddress" />
        <el-table-column label="设备状态" align="center" prop="status">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.equipment_status" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
              v-hasPermi="['equipment:RmEquipment:edit']">选择</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
        @pagination="getList" />
    </el-dialog>
  </div>
</template>

<script>
import { listRmEquipment, getRmEquipment, delRmEquipment, addRmEquipment, updateRmEquipment } from "@/api/equipment/RmEquipment";
import { listRmRooms } from "@/api/room/RmRoom";
import { listRmCabinets } from "@/api/cabinet/RmCabinet";
import { deptTreeSelect } from "@/api/system/user";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

import { listRmEquipmentModels, getRmEquipmentModel } from "@/api/model/RmEquipmentModel";

export default {
  name: "RmEquipment",
  dicts: ['equipment_status', 'equipment_type', 'sys_normal_disable'],
  components: { Treeselect },
  data() {
    return {
      dialogFormVisible: false,
      //是否弹窗全屏
      dialogFull: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 子表选中数据
      checkedRmEquipmentSlot: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 设备管理表格数据
      RmEquipmentList: [],
      // 设备槽位管理表格数据
      rmEquipmentSlotList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 位置Y时间范围
      daterangePurchaseDate: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        deptId: null,
        remarks: null,
        equipmentName: null,
        equipmentCode: null,
        equipmentType: null,
        model: null,
        brand: null,
        purchaseDate: null,
        startU: null,
        occupiedU: null,
        length: null,
        width: null,
        height: null,
        ipAddress: null,
        status: null,
        positionX: null,
        positionY: null,
        cabinetName: null,
        roomName: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        equipmentName: [
          { required: true, message: "设备名称不能为空", trigger: "blur" }
        ],
        equipmentCode: [
          { required: true, message: "设备编码不能为空", trigger: "blur" }
        ],
        equipmentType: [
          { required: true, message: "设备类型不能为空", trigger: "change" }
        ],
      },

      deptOptions: undefined,
      datas: {},
      roomInfo: {},
      // 机柜模板管理表格数据
      RmCabinetList: [],
      // 查询参数
      RoomqueryParams: {
        pageNum: 1,
        pageSize: 10,
        status: null,
        remarks: null,
        deptId: null,
        roomName: null,
        roomCode: null,
        address: null,
        length: null,
        width: null,
        height: null,
        longitude: null,
        latitud: null,
        floorCount: null,
        roomLevel: null,
        modelId: null,
        modelName: null
      },
      // 查询参数
      CabinetqueryParams: {
        roomId: null,
        roomName: null,
      },
      // 机房管理表格数据
      RmRoomList: [],
      roomInfo: {},

      // 设备模板管理表格数据
      RmEquipmentModelList: [],
      // 设备槽位模板管理表格数据
      rmEquipmentSlotModelList: [],
    };
  },
  created() {
    if (Object.keys(this.roomInfo).length > 0) {
      this.showSearch = false;
      this.queryParams.roomId = this.roomInfo.uuid;
    }
    this.getList();
    this.getDeptTree();
  },
  // watch: {
  //   roomInfo: {
  //     deep: true,
  //     handler() {
  //       this.showSearch = false;
  //       this.queryParams.roomId = this.roomInfo.uuid;
  //       this.getList();
  //       this.getDeptTree();
  //     }
  //   },
  // },
  methods: {
    /** 查询部门下拉树结构 */
    getDeptTree() {
      deptTreeSelect().then(response => {
        this.deptOptions = response.data;
      });
    },
    indexMethod(index) {
      return (
        index + (this.queryParams.pageNum - 1) * this.queryParams.pageSize + 1
      );
    },
    handleChange(val) {
      // console.log(val);
    },
    handleClick(tab, event) {
      // console.log(tab, event);
    },
    handleClose() {
      this.cancel();
    },
    /** 查询设备管理列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangePurchaseDate && '' != this.daterangePurchaseDate) {
        this.queryParams.params["beginPurchaseDate"] = this.daterangePurchaseDate[0];
        this.queryParams.params["endPurchaseDate"] = this.daterangePurchaseDate[1];
      }
      listRmEquipment(this.queryParams).then(response => {
        this.RmEquipmentList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.dialogFull = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        uuid: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        userId: null,
        deptId: null,
        filetime: null,
        remarks: null,
        delFlag: null,
        cabinetId: null,
        equipmentName: null,
        equipmentCode: null,
        equipmentType: null,
        model: null,
        brand: null,
        purchaseDate: null,
        startU: null,
        occupiedU: null,
        length: null,
        width: null,
        height: null,
        ipAddress: null,
        status: "1",
        positionX: null,
        positionY: null,
        cabinetName: null,
        roomId: null,
        roomName: null,
        modelname: null,
        modelId: null,
        modelName: null
      };
      this.rmEquipmentSlotList = [];
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangePurchaseDate = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.uuid)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加";
      this.getRoomList();
      this.getModelList();
      if (Object.keys(this.roomInfo).length > 0) {
        // this.showSearch = false;
        this.dialogFull = false;
        this.form.roomName = this.roomInfo.roomName;
        this.form.roomId = this.roomInfo.uuid;
      } else {
        this.dialogFull = true;
      }
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.uuid || this.ids
      getRmEquipment(id).then(response => {
        this.form = response.data;
        this.rmEquipmentSlotList = response.data.rmEquipmentSlotList;
        this.open = true;
        this.title = "修改";
        this.getRoomList();
        this.getModelList();
        if (Object.keys(this.roomInfo).length > 0) {
          // this.showSearch = false;
          this.dialogFull = false;
          // this.form.roomName = this.roomInfo.roomName;
          // this.form.roomId = this.roomInfo.uuid;
        } else {
          this.dialogFull = true;
        }
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.form.rmEquipmentSlotList = this.rmEquipmentSlotList;
          if (this.form.uuid != null) {
            updateRmEquipment(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addRmEquipment(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 保存按钮 */
    savesubmitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.form.rmEquipmentSlotList = this.rmEquipmentSlotList;
          addRmEquipment(this.form).then(response => {
            this.$modal.msgSuccess("保存成功");
            // this.open = false;
            this.getList();
          });
        }
      });
    },
    /** 删除按钮操作 */
    handleDeletes(row) {
      const ids = row.uuid || this.ids;
      this.$modal.confirm('是否确认删除选中的数据项？').then(function () {
        return delRmEquipment(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.uuid || this.ids;
      this.$modal.confirm('是否确认删除数据？').then(function () {
        return delRmEquipment(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 设备槽位管理序号 */
    rowRmEquipmentSlotIndex({ row, rowIndex }) {
      row.index = rowIndex + 1;
    },
    /** 设备槽位管理添加按钮操作 */
    handleAddRmEquipmentSlot() {
      let obj = {};
      obj.uuid = "";
      obj.userId = "";
      obj.deptId = "";
      obj.filetime = "";
      obj.remarks = "";
      obj.slotNumber = "";
      obj.length = "";
      obj.width = "";
      obj.height = "";
      obj.status = "";
      obj.positionX = "";
      obj.positionY = "";
      this.rmEquipmentSlotList.push(obj);
    },
    /** 设备槽位管理删除按钮操作 */
    handleDeleteRmEquipmentSlot() {
      if (this.checkedRmEquipmentSlot.length == 0) {
        this.$modal.msgError("请先选择要删除的设备槽位管理数据");
      } else {
        const rmEquipmentSlotList = this.rmEquipmentSlotList;
        const checkedRmEquipmentSlot = this.checkedRmEquipmentSlot;
        this.rmEquipmentSlotList = rmEquipmentSlotList.filter(function (item) {
          return checkedRmEquipmentSlot.indexOf(item.index) == -1
        });
      }
    },
    /** 复选框选中数据 */
    handleRmEquipmentSlotSelectionChange(selection) {
      this.checkedRmEquipmentSlot = selection.map(item => item.index)
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('equipment/RmEquipment/export', {
        ...this.queryParams
      }, `导出明细_${new Date().getTime()}.xlsx`)
    },

    /** 查询机房管理列表 */
    getRoomList() {
      this.loading = true;
      listRmRooms().then(response => {
        this.RmRoomList = response.rows;
        // this.total = response.total;
        this.loading = false;
      });
    },
    selectRoom(val) {
      const RoomMode = this.RmRoomList.find(item => item.uuid === val)
      // console.log("this.form.roomName===", this.form.roomName);
      //重新选择机房，自动更新所属机柜
      if (this.form.roomName != RoomMode.roomName || this.form.roomName == null) {
        // console.log("this.form.cabinetName===", this.form.cabinetName);
        this.form.cabinetName = "";
        this.form.cabinetId = "";
        this.RmCabinetList = [];
        this.getCabinetList(RoomMode.uuid);
      }
      this.form.roomName = RoomMode.roomName
      this.form.roomId = RoomMode.uuid
    },
    /** 查询机房机柜管理列表 */
    getCabinetList(val) {
      this.loading = true;
      this.CabinetqueryParams.roomId = val;
      listRmCabinets(this.CabinetqueryParams).then(response => {
        this.RmCabinetList = response.rows;
        this.loading = false;
      });
    },
    selectCabinet(val) {
      const RoomMode = this.RmCabinetList.find(item => item.uuid === val)
      this.form.cabinetName = RoomMode.cabinetName
      this.form.cabinetId = RoomMode.uuid
    },

    /** 查询设备模板管理列表 */
    getModelList() {
      this.loading = true;
      listRmEquipmentModels().then(response => {
        this.RmEquipmentModelList = response.rows;
        // console.log("this.RmEquipmentModelList===", this.RmEquipmentModelList);
        this.loading = false;
      });
    },
    selectModel(val) {
      const res = this.RmEquipmentModelList.find(item => item.uuid === val)
      getRmEquipmentModel(val).then(response => {
        // this.form = response.data;
        this.rmEquipmentSlotList = response.data.rmEquipmentSlotModelList;
        this.form.model = response.data.model
        this.form.equipmentType = response.data.equipmentType
        this.form.brand = response.data.brand
        this.form.manufacturer = response.data.manufacturer
        this.form.occupiedU = response.data.occupiedU
        this.form.length = response.data.length
        this.form.width = response.data.width
        this.form.height = response.data.height
        this.form.modelId = response.data.uuid
        this.form.modelName = response.data.equipmentName
      });

    },
  }
};
</script>
<style lang="scss" scoped></style>
