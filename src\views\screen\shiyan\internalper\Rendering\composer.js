import TWEEN from '@tweenjs/tween.js';

// 调整main元素的位置
function adjustMainElement() {
    const mainElement = document.querySelector('.main');
    const leftElement = document.getElementById('left');

    if (mainElement) {
        // 让浏览器在下一帧重新计算布局
        requestAnimationFrame(() => {
            // 检查左侧面板是否隐藏
            const isLeftHidden = leftElement && leftElement.classList.contains('hidden');

            // 根据左侧面板的状态调整main元素的位置
            if (isLeftHidden) {
                // 左侧面板隐藏，main元素向左移动
                mainElement.style.transform = 'translateX(-8%)';
            } else {
                // 左侧面板显示，main元素恢复原位
                mainElement.style.transform = 'translateX(0)';
            }
        });
    }
}

export function addComposer(motor, outlinePass, updateSelectedCabinet, raycaster, card) {
    // 未选中对象返回空数组[],选中一个对象，数组1个元素，选中两个对象，数组两个元素
    const intersects = raycaster.intersectObjects([motor]); // 对参数中的网格模型对象进行射线交叉计算
    if (intersects.length === 0) {
        outlinePass.selectedObjects = [];
        card.classList.add('hidden');
        document.getElementById('left').classList.remove('hidden');
        // 调整main元素的宽度和位置
        adjustMainElement();
        console.log("No intersection found");
        updateSelectedCabinet(null); // 不更新物体数据
        return;
    }
    let selectedObject = intersects[0].object; // 选中的第一个模型
    let index = outlinePass.selectedObjects.indexOf(selectedObject); // 获取选中对象在outlinePass中的索引
    const tween = new TWEEN.Tween(selectedObject.position);
    if (index === -1) { // intersects.length等于-1说明，说明outlinePass中无该模型
        outlinePass.selectedObjects.forEach(obj => { // 执行对应操作
            if (obj !== selectedObject) {
                new TWEEN.Tween(obj.position)
                    .to({ y: 11 }, 500) // 500ms 内恢复原始位置
                    .easing(TWEEN.Easing.Quadratic.Out)
                    .onComplete(() => {
                        delete obj.userData.originalPosition;
                    })
                    .start();
            }
        });
        outlinePass.selectedObjects = []; // 清空outlinePass.selectedObjects
        outlinePass.selectedObjects.push(selectedObject); // 添加选中对象
        tween // 创建一个TWEEN.Tween对象，用于控制物体的移动
            .to({ y: 30 }, 500)
            .easing(TWEEN.Easing.Quadratic.Out)
            .onComplete(() => {
                delete selectedObject.userData.originalPosition;
            })
            .start();
        card.classList.remove('hidden'); // 显示右侧面板
        document.getElementById('left').classList.add('hidden'); // 隐藏左侧面板
        // 调整main元素的宽度和位置
        adjustMainElement();

        // document.getElementById('left').classList.add('hidden'); // 显示div
    } else {
        outlinePass.selectedObjects.splice(index, 1); // 删除选中对象
        tween
            .to({ y: 11 }, 500) // 500ms 内恢复原始位置
            .easing(TWEEN.Easing.Quadratic.Out)
            .onComplete(() => {
                delete selectedObject.userData.originalPosition;
            })
            .start();
        card.classList.add('hidden'); // 隐藏右侧面板
        document.getElementById('left').classList.remove('hidden'); // 显示左侧面板
        // 调整main元素的宽度和位置
        adjustMainElement();
        // document.getElementById('left').classList.remove('hidden'); // 隐藏div
    }
    return { selectedObject, index };
}