import * as THREE from 'three';
import { Line2 } from 'three/examples/jsm/lines/Line2.js';
import { LineGeometry } from 'three/examples/jsm/lines/LineGeometry.js';
import { LineMaterial } from 'three/examples/jsm/lines/LineMaterial.js';
import { initjhj , initjhj2} from './jiaohuanji.js';
    
const params = {
    length: 1.5,    // x轴方向长度
    width: 1,      // z轴方向深度
    height: 3,     // y轴方向高度
    smallRect: {
        length: 0.55,
        height: 2.8,
        gap: 0.175,
    },
    lineWidth: 5,
    color: 0x51cdd6,
};
export function getParameters() {
    return params;
}
export async function initMotor2(cabinetData) {


    // 手动定义主长方体的12条边（无对角线）
    const halfL = params.length / 2;
    const halfH = params.height / 2;
    const halfW = params.width / 2;
    
    // 定义8个顶点
    const vertices = [
        new THREE.Vector3(-halfL, -halfH-0.135, halfW),  // 前左下
        new THREE.Vector3(halfL, -halfH-0.135, halfW),   // 前右下
        new THREE.Vector3(halfL, halfH+0.185, halfW),    // 前右上
        new THREE.Vector3(-halfL, halfH+0.185, halfW),   // 前左上
        new THREE.Vector3(-halfL, -halfH, -halfW), // 后左下
        new THREE.Vector3(halfL, -halfH, -halfW),  // 后右下
        new THREE.Vector3(halfL, halfH, -halfW),   // 后右上
        new THREE.Vector3(-halfL, halfH, -halfW),  // 后左上
    ];

    // 定义12条边的连接关系（索引对）
    const edges = [
        // 前面四条边
        [0,1], [1,2], [2,3], [3,0],
        [0,4],[4,5],[5,1],[1,2],[2,6],[6,7],[7,3],[3,0],
        // 后面四条边
        [4,5], [5,6], [6,7], [7,4]
    ];

    // 生成线段几何体
    const positions = [];
    edges.forEach(([i, j]) => {
        positions.push(
            vertices[i].x, vertices[i].y, vertices[i].z,
            vertices[j].x, vertices[j].y, vertices[j].z
        );
    });

    const mainGeometry = new LineGeometry().setPositions(positions);
    const mainLines = new Line2(
        mainGeometry,
        new LineMaterial({ color: params.color, linewidth: params.lineWidth })
    );

    // 创建背面小长方形
    const totalSmallWidth = params.smallRect.length * 2 + params.smallRect.gap;
    const marginX = (params.length - totalSmallWidth) / 2;
    const leftXStart = -halfL + marginX;
    const rightXStart = leftXStart + params.smallRect.length + params.smallRect.gap;
    const zPos = -halfW; // 背面z坐标

    function createSmallRectLine(xStart) {
        const yStart = -params.smallRect.height / 2;
        const yEnd = params.smallRect.height / 2;
        const points = [
            [xStart, yStart, zPos],
            [xStart + params.smallRect.length, yStart, zPos],
            [xStart + params.smallRect.length, yEnd, zPos],
            [xStart, yEnd, zPos],
            [xStart, yStart, zPos] // 闭合路径
        ];
        const geometry = new LineGeometry();
        const linePositions = points.flatMap(p => p);
        geometry.setPositions(linePositions);
        return new Line2(
            geometry,
            new LineMaterial({ color: params.color, linewidth: params.lineWidth })
        );
    }

    const leftRect = createSmallRectLine(leftXStart);
    const rightRect = createSmallRectLine(rightXStart);

    const group = new THREE.Group();
    group.add(mainLines, leftRect, rightRect);

    const jhj = await initjhj(cabinetData);
    // group.add(jhj);
    // mainLines.translateY(params.height/2);
    // leftRect.translateY(params.height/2);
    // rightRect.translateY(params.height/2);
    // group.translateY(params.height/2);
    // const axesHelper = new THREE.AxesHelper(100);
    // group.add(axesHelper);
    return group;
}
export async function showjhj(show3DPattern,startU,occupiedU,currentMouseNumber) {
    const jhj2 = await initjhj2(show3DPattern,startU,occupiedU,currentMouseNumber);
    return jhj2;
}