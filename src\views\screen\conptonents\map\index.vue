<template>
   <div ref="charts" style="width: 100%; height: 100%;position=absolute;"></div>
</template>
<script>
import mapCenterPoint from "@/assets/images/img/mapCenterPoint.png";
import mapPoint from "@/assets/images/img/mapPoint.png";
import bkmap from "@/assets/images/img/map.jpg";

import * as echarts from "echarts";
import handan from "@/assets/mapJson/handan.json"
// import handan from "@/assets/mapJson/hebei.json"
import wuan from "@/assets/mapJson/wuan.json"
// import wuan from "@/assets/mapJson/hebei.json"
export default {
  name: 'mapcommon',
  data(){
    return{
 
    }
  },
  created () {
    this.$nextTick(() => {
      this.initCharts();
    })
  },
  methods: {
     initCharts() {
      const charts = echarts.init(this.$refs["charts"]);
      //飞线样式
       let coord = [
      {
        name: "邯郸市",
        value: [114.543709, 36.625316, 1],
        symbolSize: 8,
        itemStyle: {
          normal: {
            color: "#F58158",
          },
        },
      },
      {
        name: "武安",
        value: [114.171219, 36.691812, 1],
        symbolSize: 8,
        itemStyle: {
          normal: {
            color: "#2B8BD8",
          },
        },
      },
      {
        name: "永年",
        value: [114.489679, 36.762316, 1],
        symbolSize: 8,
        itemStyle: {
          normal: {
            color: "#2B8BD8",
          },
        },
      },

        {
        name: "涉县",
        value: [113.690990,36.580164, 1],
        symbolSize: 8,
        itemStyle: {
          normal: {
            color: "#2B8BD8",
          },
        },
      },
        {
        name: "磁山",
        value: [114.098370,36.573606, 1],
        symbolSize: 8,
        itemStyle: {
          normal: {
            color: "#2B8BD8",
          },
        },
      },
        {
        name: "峰峰",
        value: [114.242197,36.428790, 1],
        symbolSize: 8,
        itemStyle: {
          normal: {
            color: "#2B8BD8",
          },
        },
      },
        {
        name: "磁县",
        value: [114.385722,36.376353, 1],
        symbolSize: 8,
        itemStyle: {
          normal: {
            color: "#2B8BD8",
          },
        },
      },
        {
        name: "临漳",
        value: [114.611484,36.336865, 1],
        symbolSize: 8,
        itemStyle: {
          normal: {
            color: "#2B8BD8",
          },
        },
      },
        {
        name: "魏县",
        value: [114.921260,36.361550, 1],
        symbolSize: 8,
        itemStyle: {
          normal: {
            color: "#2B8BD8",
          },
        },
      },
        {
        name: "成安",
        value: [114.693447,36.456025, 1],
        symbolSize: 8,
        itemStyle: {
          normal: {
            color: "#2B8BD8",
          },
        },
      },
        {
        name: "肥乡",
        value: [114.813785,36.558220, 1],
        symbolSize: 8,
        itemStyle: {
          normal: {
            color: "#2B8BD8",
          },
        },
      },
        {
        name: "曲周",
        value: [114.978635,36.773915, 1],
        symbolSize: 8,
        itemStyle: {
          normal: {
            color: "#2B8BD8",
          },
        },
      },
        {
        name: "鸡泽",
        value: [114.888100,36.913719, 1],
        symbolSize: 8,
        itemStyle: {
          normal: {
            color: "#2B8BD8",
          },
        },
      },
        {
        name: "邱县",
        value: [115.188219,36.808567, 1],
        symbolSize: 8,
        itemStyle: {
          normal: {
            color: "#2B8BD8",
          },
        },
      },
        {
        name: "馆陶",
        value: [115.273237,36.543773, 1],
        symbolSize: 8,
        itemStyle: {
          normal: {
            color: "#2B8BD8",
          },
        },
      },
        {
        name: "大名",
        value: [115.136432,36.301738, 1],
        symbolSize: 8,
        itemStyle: {
          normal: {
            color: "#2B8BD8",
          },
        },
      },
        {
        name: "广平",
        value: [114.947457,36.483902, 1],
        symbolSize: 8,
        itemStyle: {
          normal: {
            color: "#2B8BD8",
          },
        },
      },
    ];
    let lines_coord = [];
    coord.forEach((v, index) => {
      index > 0 &&
        lines_coord.push({
          coords: [v.value, coord[0].value],
        });
    });
    lines_coord = [
      {
        //枢纽楼至磁山
        coords: [
          [114.098370,36.573606],
          [114.543709, 36.625316],
        ],
      },
      {
        //磁山至涉县
        coords: [
          [113.690990,36.580164],
          [114.098370,36.573606],
        ],
      },
      {
        //涉县至武安
        coords: [
          [113.690990,36.580164],
          [114.171219,36.691812],
        ],
      },
      {
        //武安至枢纽楼
        coords: [
          [114.171219,36.691812],
          [114.543709, 36.625316],
        ],
      },
      {
        //武安至永年
        coords: [
          [114.171219,36.691812],
          [114.489679,36.762316],
        ],
      },
      {
        //永年至枢纽楼
        coords: [
          [114.489679,36.762316],
          [114.543709, 36.625316],
        ],
      },
      {
        //永年至鸡泽
        coords: [
          [114.489679,36.762316],
          [114.888100,36.913719],
        ],
      },    
      {
        //永年至曲周
        coords: [
          [114.489679,36.762316],
          [114.978635,36.773915],
        ],
      },
      {
        //曲周至枢纽楼
        coords: [
          [114.978635,36.773915],
          [114.543709, 36.625316],
        ],
      },
      {
        //曲周至邱县
        coords: [
          [115.188219,36.808567],
          [114.978635,36.773915],
        ],
      },
      {
        //邱县至馆陶
        coords: [
          [115.188219,36.808567],
          [115.273237,36.543773],
        ],
      },
      {
        //馆陶至广平
        coords: [
          [115.273237,36.543773],
          [114.947457,36.483902],
        ],
      },
      {
        //广平至枢纽
        coords: [
          [114.947457,36.483902],
          [114.543709, 36.625316],
        ],
      },      {
        //广平至大名
        coords: [
          [115.136432,36.301738],
          [114.947457,36.483902],
        ],
      },      
      {
        //广平至魏县
        coords: [
          [114.947457,36.483902],
          [114.921260,36.361550],
        ],
      },      
      {
        //馆陶至大名
        coords: [
          [115.273237,36.543773],
          [115.136432,36.301738],
        ],
      },      
      {
        //大名至魏县
        coords: [
          [115.136432,36.301738],
          [114.921260,36.361550],
        ],
      },      
      {
        //魏县至成安
        coords: [
          [114.921260,36.361550],
          [114.693447,36.456025],
        ],
      },      
      {
        //成安至肥乡
        coords: [
          [114.693447,36.456025],
          [114.813785,36.558220],
        ],
      },      
      {
        //肥乡至枢纽楼
        coords: [
          [114.813785,36.558220],
          [114.543709, 36.625316],
        ],
      },      
      {
        //曲周至肥乡
        coords: [
          [114.978635,36.773915],
          [114.813785,36.558220],
        ],
      },      
      {
        //成安至枢纽楼
        coords: [
          [114.693447,36.456025],
          [114.543709, 36.625316],
        ],
      },      {
        //魏县至临漳
        coords: [
          [114.171219, 36.691812],
          [114.489679, 36.762316],
        ],
      },      
      {
        //临漳至枢纽楼
        coords: [
          [114.611484,36.336865],
          [114.543709, 36.625316],
        ],
      },      
      {
        //临漳至磁县
        coords: [
          [114.611484,36.336865],
          [114.385722,36.376353],
        ],
      },
      {
        //磁县至枢纽楼
        coords: [
          [114.385722,36.376353],
          [114.543709, 36.625316],
        ],
      },
      {
        //磁县至峰峰
        coords: [
          [114.385722,36.376353],
          [114.242197,36.428790],
        ],
      },
      {
        //峰峰至枢纽楼
        coords: [
          [114.242197,36.428790],
          [114.543709, 36.625316],
        ],
      },
      {
        //涉县至峰峰
        coords: [
          [113.690990,36.580164],
          [114.242197,36.428790],
        ],
      },
         {
        //鸡泽至邱县
        coords: [
          [114.888100,36.913719],
          [115.188219,36.808567],
        ],
      },
    ];
    //飞线结束
      const option = {
        // 背景颜色
        // backgroundColor: "#404a59",
        // 提示浮窗样式
        tooltip: {
          show: true,
          trigger: "item",
          alwaysShowContent: false,
          backgroundColor: "#0C121C",
          borderColor: "rgba(0, 0, 0, 0.16);",
          hideDelay: 100,
          triggerOn: "mousemove",
          enterable: true,
          textStyle: {
            color: "#DADADA",
            fontSize: "12",
            width: 20,
            height: 30,
            overflow: "break",
          },
          showDelay: 100
        },
        // 地图配置
        geo: {
          map: "邯郸市",
          label: {
            // 通常状态下的样式
            normal: {
              show: false,
              textStyle: {
                color: "#fff",
              },
              emphasis: {
                show: true,
              },
            },
            // 鼠标放上去的样式
            emphasis: {
              //是否区县名称显示文字，如果显示就true
              show: false,
              textStyle: {
                color: "#fff",
              },
            },
          },
          // 地图区域的样式设置
          itemStyle: {
            normal: {
              borderColor: "rgba(147, 235, 248, 1)",
                //  borderColor: "rgba(26, 10, 242, 1)",
              borderWidth: 1,
              areaColor: {
                type: "radial",
                x: 0.5,
                y: 0.5,
                r: 0.8,
                colorStops: [
                  {
                    offset: 0,
                    color: "rgba(22, 56, 111, 0)", // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: "rgba(22, 56, 111, .2)", // 100% 处的颜色
                  },
                ],
                globalCoord: false, // 缺省为 false
              },
              shadowColor: "rgba(128, 217, 248, 1)",
              shadowOffsetX: -2,
              shadowOffsetY: 2,
              shadowBlur: 10,
            },
            // 鼠标放上去高亮的样式
            emphasis: {
              areaColor: "#389BB7",
              borderWidth: 0,
            },
          },
        },
            //飞线
          series: [
            {
              name: "邯郸市",
              type: "effectScatter",
              coordinateSystem: "geo",
              // symbol:'image://./static/guizi/images/044br.gif',
              symbolSize: 30,
              zlevel: 2,
              rippleEffect: {
                brushType: "stroke",
              },
              label: {
                normal: {
                  show: true,
                  formatter: "{b}",
                  position: "right",
                  textStyle: {
                    color: "#fff",
                    fontSize: 9,
                  },
                },
              },
              symbolSize: 8,
              showEffectOn: "render",
              itemStyle: {
                normal: {
                  color: "#46bee9",
                },
              },
              data: coord.slice(0, 17),
            },
            {
              type: "lines",
              coordinateSystem: "geo",
              zlevel: 15,
              effect: {
                show: true,
                constantSpeed: 30,
                symbol: "pin",
                symbolSize: 3,
                trailLength: 0,
              },
              lineStyle: {
                normal: {
                  color: new echarts.graphic.LinearGradient(
                    0,
                    0,
                    0,
                    1,
                    [
                      {
                        offset: 0,
                        color: "#58B3CC",
                      },
                      {
                        offset: 1,
                        color: "#F58158",
                      },
                    ],
                    false
                  ),
                  width: 3,
                  opacity: 0.5,
                  curveness: 0.2, //曲线率
                  smooth: 0.1,
                },
              },
              data: lines_coord.slice(0, 30),
            },
          ],
//飞线结束
        
      };
      // 地图注册，第一个参数的名字必须和option.geo.map一致
      echarts.registerMap("邯郸市",handan)
      charts.setOption(option);
    },
  }

}
</script>