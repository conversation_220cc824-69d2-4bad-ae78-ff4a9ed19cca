import * as THREE from 'three';

const geometry = new THREE.BufferGeometry(); 
const vertices = new Float32Array([
    0, 0, 0, //顶点1坐标
    80, 0, 0, //顶点2坐标
    80, 80, 0, //顶点3坐标
    0, 80, 0, //顶点4坐标
]);
geometry.attributes.position = new THREE.BufferAttribute(vertices, 3);//各个点位置
const indexs = new Uint16Array([
    0,1,2,0,2,3
]);
const index = new THREE.BufferAttribute(indexs, 1); 
geometry.setIndex(index);//设置点索引

// const points = new THREE.PointsMaterial({
//     color: 0xff0000,
//     size: 10,
// });

// const line = new THREE.LineBasicMaterial({
//     color: 0xff0000,
// });
// const mesh = new THREE.Points(geometry,points);
const material = new THREE.MeshBasicMaterial({
    color: 0xff0000,
    wireframe: true,
});
const qiu = new THREE.SphereGeometry(10, 128, 128);
qiu.scale(2, 2, 2);

// 几何体旋转、缩放或平移之后，查看几何体顶点位置坐标的变化
// BufferGeometry的旋转、缩放、平移等方法本质上就是改变顶点的位置坐标
console.log('顶点位置数据', qiu.attributes.position);
const mesh = new THREE.Mesh(qiu, material);
console.log( "mesh",mesh);
export default mesh;