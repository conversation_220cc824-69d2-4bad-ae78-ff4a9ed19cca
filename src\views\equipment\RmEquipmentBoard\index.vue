<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="120px">
      <el-form-item label="所属区域" prop="deptId">
        <treeselect v-model="queryParams.deptId" :options="deptOptions" :show-count="true" placeholder="请选择归属部门"
          style="width:240px" />
      </el-form-item>
      <el-form-item label="所属机房" prop="roomName">
        <el-input v-model="queryParams.roomName" placeholder="请输入所属机房" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="所属设备" prop="equipmentName">
        <el-input v-model="queryParams.equipmentName" placeholder="请输入所属设备" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <!-- <el-form-item label="所属槽位号" prop="slotNumber">
        <el-input
          v-model="queryParams.slotNumber"
          placeholder="请输入所属槽位号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="板卡名称" prop="boardName">
        <el-input v-model="queryParams.boardName" placeholder="请输入板卡名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="板卡号" prop="boardNumber">
        <el-input v-model="queryParams.boardNumber" placeholder="请输入板卡号" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <!-- <el-form-item label="板卡品牌" prop="brand">
        <el-input
          v-model="queryParams.brand"
          placeholder="请输入板卡品牌"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="板卡型号" prop="model">
        <el-input v-model="queryParams.model" placeholder="请输入板卡型号" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <!-- <el-form-item label="长度" prop="length">
        <el-input
          v-model="queryParams.length"
          placeholder="请输入长度"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="宽度" prop="width">
        <el-input
          v-model="queryParams.width"
          placeholder="请输入宽度"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="高度" prop="height">
        <el-input
          v-model="queryParams.height"
          placeholder="请输入高度"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="板卡状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择板卡状态" clearable>
          <el-option v-for="dict in dict.type.sys_normal_disable" :key="dict.value" :label="dict.label"
            :value="dict.value" />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="所在设备位置x" prop="positionX">
        <el-input
          v-model="queryParams.positionX"
          placeholder="请输入所在设备位置x"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="所在设备位置y" prop="positionY">
        <el-input
          v-model="queryParams.positionY"
          placeholder="请输入所在设备位置y"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->

      <!-- <el-form-item label="所属模板" prop="modelName">
        <el-input
          v-model="queryParams.modelName"
          placeholder="请输入所属模板"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['equipment:RmEquipmentBoard:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
          v-hasPermi="['equipment:RmEquipmentBoard:edit']">修改</el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDeletes"
          v-hasPermi="['equipment:RmEquipmentBoard:removes']">删除</el-button>
      </el-col> -->
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
          v-hasPermi="['equipment:RmEquipmentBoard:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="RmEquipmentBoardList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column prop="" label="序号" width="80" align="center" type="index" :index="indexMethod" />
      <el-table-column label="所属区域" align="center" prop="deptId" />
      <el-table-column label="所属机房" align="center" prop="roomName" />
      <el-table-column label="所属设备" align="center" prop="equipmentName" />
      <el-table-column label="所属槽位号" align="center" prop="slotNumber" />
      <el-table-column label="板卡名称" align="center" prop="boardName" />
      <!-- <el-table-column label="板卡类型" align="center" prop="boardType" /> -->
      <el-table-column label="板卡号" align="center" prop="boardNumber" />
      <!-- <el-table-column label="板卡品牌" align="center" prop="brand" /> -->
      <el-table-column label="板卡型号" align="center" prop="model" />
      <!-- <el-table-column label="长度" align="center" prop="length" />
      <el-table-column label="宽度" align="center" prop="width" />
      <el-table-column label="高度" align="center" prop="height" /> -->
      <el-table-column label="板卡状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status" />
        </template>
      </el-table-column>
      <!-- <el-table-column label="所在设备位置x" align="center" prop="positionX" />
      <el-table-column label="所在设备位置y" align="center" prop="positionY" />
      <el-table-column label="所属模板" align="center" prop="modelName" /> -->
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-search" @click="handleView(scope.row)"
            v-hasPermi="['equipment:RmEquipmentBoard:edit']">查看</el-button>
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['equipment:RmEquipmentBoard:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['equipment:RmEquipmentBoard:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改设备板卡管理对话框 -->
    <el-dialog :title="title" :fullscreen="dialogFull" :visible.sync="openBoardDialogVisible" width="70%" append-to-body
      :close-on-click-modal="false" @close="handleClose()">
      <template slot="title">
        <div class="avue-crud__dialog__header" style="border-bottom:1px solid #DCDFE6;padding-bottom:15px;">
          <span class="el-dialog__title">
            <span
              style="display:inline-block;background-color: #3478f5;width:3px;height:20px;margin-right:5px; float: left;margin-top:2px;margin-right:15px"></span>
            {{ title }}
          </span>
          <div class="avue-crud__dialog__menu" @click="dialogFull ? dialogFull = false : dialogFull = true">
            <i class="el-icon-full-screen"></i>
          </div>
        </div>
      </template>
      <div class="content">
        <el-form ref="form" :model="form" :rules="rules" label-width="120px">
          <!-- <el-tabs v-model="activeName" @tab-click="handleClick">
            <el-tab-pane label="基本信息" name="first"> -->
          <el-row>
            <el-col :span="8">
              <el-form-item label="所属模板" prop="modelName">
                <el-select v-model="form.modelName" placeholder="请选择模板" filterable clearable @change="selectModel"
                  style="width:100%">
                  <el-option v-for="(item, index) in RmEquipmentBoardModelList" :key="index" :label="item.boardName"
                    :value="item.uuid">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="所属槽位号" prop="slotNumber">
                <el-input v-model="form.slotNumber" placeholder="请输入所属槽位号" readonly v-if="disable" />
                <el-select v-model="form.slotNumber" placeholder="请选所属槽位号" filterable clearable
                  @change="selectEqumentSlot" style="width:100%" v-else>
                  <el-option v-for="(item, index) in rmEquipmentSlotList" :key="index" :label="item.slotNumber"
                    :value="item.uuid">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="板卡名称" prop="boardName">
                <el-input v-model="form.boardName" placeholder="请输入板卡名称" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label="板卡号" prop="boardNumber">
                <el-input v-model="form.boardNumber" placeholder="请输入板卡号" @input="selectboardNumber" readonly
                  v-if="disable" />
                <el-input v-model="form.boardNumber" placeholder="请输入板卡号" @input="selectboardNumber" v-else />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="板卡型号" prop="model">
                <el-input v-model="form.model" placeholder="请输入板卡型号" readonly />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="板卡品牌" prop="brand">
                <el-input v-model="form.brand" placeholder="请输入板卡品牌" readonly />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label="长度(mm)" prop="length">
                <el-input v-model="form.length" placeholder="请输入长度" readonly />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="宽度(mm)" prop="width">
                <el-input v-model="form.width" placeholder="请输入宽度" readonly />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="高度(mm)" prop="height">
                <el-input v-model="form.height" placeholder="请输入高度" readonly />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label="所在设备位置x" prop="positionX">
                <el-input v-model="form.positionX" placeholder="请输入所在设备位置x" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="所在设备位置y" prop="positionY">
                <el-input v-model="form.positionY" placeholder="请输入所在设备位置y" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="板卡状态">
                <el-radio-group v-model="form.status">
                  <el-radio v-for="dict in dict.type.sys_normal_disable" :key="dict.value" :label="dict.value">{{
                    dict.label
                  }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="备注说明" prop="remarks">
                <el-input v-model="form.remarks" type="textarea" placeholder="请输入内容" />
              </el-form-item>
            </el-col>
          </el-row>
          <!-- </el-tab-pane>
            <el-tab-pane label="端口信息" name="second"> -->
          <el-divider content-position="center">端口信息</el-divider>
          <!-- <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
              <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAddRmEquipmentPort">添加</el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button type="danger" icon="el-icon-delete" size="mini"
                @click="handleDeleteRmEquipmentPort">删除</el-button>
            </el-col>
          </el-row> -->
          <el-table :data="rmEquipmentPortList" :row-class-name="rowRmEquipmentPortIndex"
            @selection-change="handleRmEquipmentPortSelectionChange" ref="rmEquipmentPort" stripe border>
            <el-table-column type="selection" width="50" align="center" />
            <el-table-column label="序号" align="center" prop="index" width="80" />
            <el-table-column label="板卡编号" prop="boardName" align="center">
              <!-- <template slot-scope="scope">
                <el-input v-model="scope.row.boardName" placeholder="请输入板卡编号" />
              </template> -->
            </el-table-column>
            <el-table-column label="端口编号" prop="portNumber" align="center">
              <!-- <template slot-scope="scope">
                <el-input v-model="scope.row.portNumber" placeholder="请输入端口编号" />
              </template> -->
            </el-table-column>
            <el-table-column label="端口类型" prop="portType" align="center">
              <template slot-scope="scope">
                <dict-tag :options="dict.type.port_type" :value="scope.row.portType" />
                <!-- <el-select v-model="scope.row.portType" placeholder="请选择端口类型">
                  <el-option v-for="dict in dict.type.port_type" :key="dict.value" :label="dict.label"
                    :value="dict.value"></el-option>
                </el-select> -->
              </template>
            </el-table-column>
            <el-table-column label="端口状态" prop="status" align="center">
              <template slot-scope="scope">
                <dict-tag :options="dict.type.equipment_port_status" :value="scope.row.status" />
                <!-- <el-select v-model="scope.row.status" placeholder="请选择端口状态">
                  <el-option v-for="dict in dict.type.equipment_port_status" :key="dict.value" :label="dict.label"
                    :value="dict.value"></el-option>
                </el-select> -->
              </template>
            </el-table-column>
            <el-table-column label="所在位置x" prop="positionX" align="center">
              <!-- <template slot-scope="scope">
                <el-input v-model="scope.row.positionX" placeholder="请输入所在板卡位置x" />
              </template> -->
            </el-table-column>
            <el-table-column label="所在位置y" prop="positionY" align="center">
              <!-- <template slot-scope="scope">
                <el-input v-model="scope.row.positionY" placeholder="请输入所在板卡位置y" />
              </template> -->
            </el-table-column>
          </el-table>
          <!-- </el-tab-pane>
          </el-tabs> -->
        </el-form>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>


    <!-- 查看设备板卡管理对话框 -->
    <el-dialog :title="title" :fullscreen="dialogFull" :visible.sync="openview" width="70%" append-to-body
      :close-on-click-modal="false" @close="handleClose()">
      <template slot="title">
        <div class="avue-crud__dialog__header" style="border-bottom:1px solid #DCDFE6;padding-bottom:15px;">
          <span class="el-dialog__title">
            <span
              style="display:inline-block;background-color: #3478f5;width:3px;height:20px;margin-right:5px; float: left;margin-top:2px;margin-right:15px"></span>
            {{ title }}
          </span>
          <div class="avue-crud__dialog__menu" @click="dialogFull ? dialogFull = false : dialogFull = true">
            <i class="el-icon-full-screen"></i>
          </div>
        </div>
      </template>
      <div class="content">
        <el-form ref="form" :model="form" :rules="rules" label-width="120px">
          <!-- <el-tabs v-model="activeName" @tab-click="handleClick">
            <el-tab-pane label="基本信息" name="first"> -->
          <el-row>
            <el-col :span="8">
              <el-form-item label="所属模板：" prop="modelName">
                <el-input v-model="form.modelName" placeholder="请输入所属模板" readonly />
                <!-- <el-select v-model="form.modelName" placeholder="请选择模板" filterable clearable @change="selectModel"
                  style="width:100%">
                  <el-option v-for="(item, index) in RmEquipmentBoardModelList" :key="index" :label="item.boardName"
                    :value="item.uuid">
                  </el-option>
                </el-select> -->
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="所属槽位号：" prop="slotNumber">
                <el-input v-model="form.slotNumber" placeholder="请输入所属槽位号" readonly />
                <!-- <el-select v-model="form.slotNumber" placeholder="请选所属槽位号" filterable clearable
                  @change="selectEqumentSlot" style="width:100%">
                  <el-option v-for="(item, index) in rmEquipmentSlotList" :key="index" :label="item.slotNumber"
                    :value="item.uuid">
                  </el-option>
                </el-select> -->
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="板卡名称：" prop="boardName">
                <el-input v-model="form.boardName" placeholder="请输入板卡名称" readonly />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label="板卡号：" prop="boardNumber">
                <el-input v-model="form.boardNumber" placeholder="请输入板卡号" readonly />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="板卡型号：" prop="model">
                <el-input v-model="form.model" placeholder="请输入板卡型号" readonly />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="板卡品牌：" prop="brand">
                <el-input v-model="form.brand" placeholder="请输入板卡品牌" readonly />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label="长度(mm)：" prop="length">
                <el-input v-model="form.length" placeholder="请输入长度" readonly />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="宽度(mm)：" prop="width">
                <el-input v-model="form.width" placeholder="请输入宽度" readonly />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="高度(mm)：" prop="height">
                <el-input v-model="form.height" placeholder="请输入高度" readonly />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label="所在设备位置x：" prop="positionX">
                <el-input v-model="form.positionX" placeholder="请输入所在设备位置x" readonly />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="所在设备位置y：" prop="positionY">
                <el-input v-model="form.positionY" placeholder="请输入所在设备位置y" readonly />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="板卡状态：">
                <dict-tag :options="dict.type.sys_normal_disable" :value="form.status" />
                <!-- <el-radio-group v-model="form.status">
                  <el-radio v-for="dict in dict.type.sys_normal_disable" :key="dict.value" :label="dict.value">{{
                    dict.label
                  }}</el-radio>
                </el-radio-group> -->
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="备注说明：" prop="remarks">
                <el-input v-model="form.remarks" type="textarea" placeholder="请输入内容" readonly />
              </el-form-item>
            </el-col>
          </el-row>
          <!-- </el-tab-pane>
            <el-tab-pane label="端口信息" name="second"> -->
          <el-divider content-position="center">端口信息</el-divider>
          <!-- <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
              <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAddRmEquipmentPort">添加</el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button type="danger" icon="el-icon-delete" size="mini"
                @click="handleDeleteRmEquipmentPort">删除</el-button>
            </el-col>
          </el-row> -->
          <el-table :data="rmEquipmentPortList" :row-class-name="rowRmEquipmentPortIndex"
            @selection-change="handleRmEquipmentPortSelectionChange" ref="rmEquipmentPort" stripe border>
            <!-- <el-table-column type="selection" width="50" align="center" /> -->
            <el-table-column label="序号" align="center" prop="index" width="80" />
            <el-table-column label="板卡编号" prop="boardName" align="center">
              <!-- <template slot-scope="scope">
                <el-input v-model="scope.row.boardName" placeholder="请输入板卡编号" />
              </template> -->
            </el-table-column>
            <el-table-column label="端口编号" prop="portNumber" align="center">
              <!-- <template slot-scope="scope">
                <el-input v-model="scope.row.portNumber" placeholder="请输入端口编号" />
              </template> -->
            </el-table-column>
            <el-table-column label="端口类型" prop="portType" align="center">
              <template slot-scope="scope">
                <dict-tag :options="dict.type.port_type" :value="scope.row.portType" />
                <!-- <el-select v-model="scope.row.portType" placeholder="请选择端口类型">
                  <el-option v-for="dict in dict.type.port_type" :key="dict.value" :label="dict.label"
                    :value="dict.value"></el-option>
                </el-select> -->
              </template>
            </el-table-column>
            <el-table-column label="端口状态" prop="status" align="center">
              <template slot-scope="scope">
                <dict-tag :options="dict.type.equipment_port_status" :value="scope.row.status" />
                <!-- <el-select v-model="scope.row.status" placeholder="请选择端口状态">
                  <el-option v-for="dict in dict.type.equipment_port_status" :key="dict.value" :label="dict.label"
                    :value="dict.value"></el-option>
                </el-select> -->
              </template>
            </el-table-column>
            <el-table-column label="所在位置x" prop="positionX" align="center">
              <!-- <template slot-scope="scope">
                <el-input v-model="scope.row.positionX" placeholder="请输入所在板卡位置x" />
              </template> -->
            </el-table-column>
            <el-table-column label="所在位置y" prop="positionY" align="center">
              <!-- <template slot-scope="scope">
                <el-input v-model="scope.row.positionY" placeholder="请输入所在板卡位置y" />
              </template> -->
            </el-table-column>
          </el-table>
          <!-- </el-tab-pane>
          </el-tabs> -->
        </el-form>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listRmEquipmentBoard, getRmEquipmentBoard, delRmEquipmentBoards, delRmEquipmentBoard, addRmEquipmentBoard, updateRmEquipmentBoard } from "@/api/equipment/RmEquipmentBoard";
import { listRmRooms, getRmRoom } from "@/api/room/RmRoom";
import { deptTreeSelect } from "@/api/system/user";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import { listRmEquipments, getRmEquipment } from "@/api/equipment/RmEquipment";
import { listRmEquipmentBoardModels, getRmEquipmentBoardModel } from "@/api/model/RmEquipmentBoardModel";

export default {
  name: "RmEquipmentBoard",
  dicts: ['port_type', 'equipment_port_status', 'sys_normal_disable'],
  components: { Treeselect },
  props: {
    quipmentInfo: {
      type: Object,
      default: () => ({}) // 默认值为空对象
    }
  },
  data() {
    return {
      activeName: 'first',
      //是否弹窗全屏
      dialogFull: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 子表选中数据
      checkedRmEquipmentPort: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 设备板卡管理表格数据
      RmEquipmentBoardList: [],
      // 设备端口管理表格数据
      rmEquipmentPortList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      openBoardDialogVisible: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        deptId: null,
        slotNumber: null,
        boardName: null,
        boardType: null,
        boardNumber: null,
        brand: null,
        model: null,
        length: null,
        width: null,
        height: null,
        status: null,
        positionX: null,
        positionY: null,
        roomName: null,
        equipmentName: null,
        modelName: null,
        slotId: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      },
      deptOptions: undefined,
      datas: {},
      roomInfo: {},
      // 机柜模板管理表格数据
      RmCabinetList: [],
      // 查询参数
      RoomqueryParams: {
        pageNum: 1,
        pageSize: 10,
        status: null,
        remarks: null,
        deptId: null,
        roomName: null,
        roomCode: null,
        address: null,
        length: null,
        width: null,
        height: null,
        longitude: null,
        latitud: null,
        floorCount: null,
        roomLevel: null,
        modelId: null,
        modelName: null,
      },
      // 查询参数
      CabinetqueryParams: {
        roomId: null,
        roomName: null,
      },
      // 机房管理表格数据
      RmRoomList: [],
      roomInfo: {},
      // 设备板卡模板管理表格数据
      RmEquipmentBoardModelList: [],
      // 查询参数
      // 设备管理表格数据
      RmEquipmentList: [],
      // 设备槽位管理表格数据
      rmEquipmentSlotList: [],
      // 查询参数
      EqumentqueryParams: {
        roomId: null,
        roomName: null,
      },
      openview: false,
      disable: false,


    };
  },
  created() {
     console.log("ccc");
    if (Object.keys(this.quipmentInfo).length > 0) {
      this.showSearch = false;
      this.queryParams.roomId = this.quipmentInfo.uuid;
      this.queryParams.equipmentName = this.quipmentInfo.equipmentName;
    }
    this.getList();
    this.getDeptTree();
    // console.log("this.quipmentInfo1===", this.quipmentInfo);
  },
  watch: {
    quipmentInfo: {
      deep: true,
      handler() {
        this.showSearch = false;
        this.queryParams.roomId = this.quipmentInfo.uuid;
        this.queryParams.equipmentName = this.quipmentInfo.equipmentName;
        // this.getList();
        // this.getDeptTree();
        console.log("this.quipmentInfo2===", this.quipmentInfo);
      }
    },
  },
  methods: {
    openDialog(res) {
      this.disable = true;
      this.handleAdd();
      this.form.slotId = res.uuid;
      this.form.slotNumber = res.slotNumber;
      this.form.boardNumber = res.slotNumber;
    },
    openviewDialog(res) {
      console.log("res===", res);
      this.showSearch = false;
      this.disable = true;
      this.queryParams.slotId = res.uuid;
      this.queryParams.roomId = this.quipmentInfo.uuid;
      this.queryParams.equipmentName = this.quipmentInfo.equipmentName;
      console.log("this.queryParams===", this.queryParams);
      this.getList();
    },
    /** 查询部门下拉树结构 */
    getDeptTree() {
      deptTreeSelect().then(response => {
        this.deptOptions = response.data;
      });
    },
    indexMethod(index) {
      return (
        index + (this.queryParams.pageNum - 1) * this.queryParams.pageSize + 1
      );
    },
    handleChange(val) {
      // console.log(val);
    },
    handleClick(tab, event) {
      // console.log(tab, event);
    },
    handleClose() {
      this.cancel();
    },
    /** 查询设备板卡管理列表 */
    getList() {
      this.loading = true;
      listRmEquipmentBoard(this.queryParams).then(response => {
        this.RmEquipmentBoardList = response.rows;
        console.log("this.RmEquipmentBoardList===", this.RmEquipmentBoardList);
        
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.openBoardDialogVisible = false;
      this.openview = false;
      this.dialogFull = false;
      this.disable = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        uuid: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        userId: null,
        deptId: null,
        filetime: null,
        remarks: null,
        delFlag: null,
        slotId: null,
        slotNumber: null,
        boardName: null,
        boardType: null,
        boardNumber: null,
        brand: null,
        model: null,
        length: null,
        width: null,
        height: null,
        status: "1",
        positionX: null,
        positionY: null,
        roomId: null,
        roomName: null,
        equipmentId: null,
        equipmentName: null,
        modelId: null,
        modelName: null
      };
      this.rmEquipmentPortList = [];
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.uuid)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.openBoardDialogVisible = true;
      this.title = "添加";
      this.form.equipmentName = this.quipmentInfo.equipmentName;
      this.form.equipmentId = this.quipmentInfo.uuid;
      this.form.roomName = this.quipmentInfo.roomName;
      this.form.roomId = this.quipmentInfo.roomId;
      // this.form.deptId = this.quipmentInfo.deptId;
      this.selectEqument(this.form.equipmentId)
      // this.selectRoom(this.quipmentInfo.roomId)
      // console.log("this.form===",this.form);
      // this.getRoomList();
      this.getModelList();
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.uuid || this.ids
      getRmEquipmentBoard(id).then(response => {
        this.form = response.data;
        this.rmEquipmentPortList = response.data.rmEquipmentPortList;
        this.openBoardDialogVisible = true;
        this.title = "修改";
        this.selectEqument(this.form.equipmentId)
      });

      this.getModelList()
    },
    handleView(row) {
      this.reset();
      const id = row.uuid
      getRmEquipmentBoard(id).then(response => {
        this.form = response.data;
        this.rmEquipmentPortList = response.data.rmEquipmentPortList;
        this.openview = true;
        this.title = "查看";
      });

      // this.selectEqumentSlot(id)
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.form.rmEquipmentPortList = this.rmEquipmentPortList;
          if (this.form.uuid != null) {
            updateRmEquipmentBoard(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.openBoardDialogVisible = false;
              this.getList();
            });
          } else {
            addRmEquipmentBoard(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.openBoardDialogVisible = false;
              this.$emit('boardAdded');
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDeletes(row) {
      const ids = this.ids;
      this.$modal.confirm('是否确认删除选中的数据项？').then(function () {
        return delRmEquipmentBoards(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
        this.$emit('boardAdded');
      }).catch(() => { });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.uuid;
      this.$modal.confirm('是否确认删除数据？').then(function () {
        return delRmEquipmentBoard(id);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
        this.$emit('boardAdded');
      }).catch(() => { });
    },
    /** 设备端口管理序号 */
    rowRmEquipmentPortIndex({ row, rowIndex }) {
      row.index = rowIndex + 1;
    },
    /** 设备端口管理添加按钮操作 */
    handleAddRmEquipmentPort() {
      let obj = {};
      obj.remarks = "";
      obj.portNumber = "";
      obj.portType = "";
      obj.status = "";
      obj.positionX = "";
      obj.positionY = "";
      if (this.form.boardNumber != null || this.form.boardNumber != "") {
        obj.boardName = this.form.boardNumber
      } else {
        obj.boardName = "";
      }
      this.rmEquipmentPortList.push(obj);
    },
    /** 设备端口管理删除按钮操作 */
    handleDeleteRmEquipmentPort() {
      if (this.checkedRmEquipmentPort.length == 0) {
        this.$modal.msgError("请先选择要删除的设备端口管理数据");
      } else {
        const rmEquipmentPortList = this.rmEquipmentPortList;
        const checkedRmEquipmentPort = this.checkedRmEquipmentPort;
        this.rmEquipmentPortList = rmEquipmentPortList.filter(function (item) {
          return checkedRmEquipmentPort.indexOf(item.index) == -1
        });
      }
    },
    /** 复选框选中数据 */
    handleRmEquipmentPortSelectionChange(selection) {
      this.checkedRmEquipmentPort = selection.map(item => item.index)
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('equipment/RmEquipmentBoard/export', {
        ...this.queryParams
      }, `导出明细_${new Date().getTime()}.xlsx`)
    },
    /** 查询机房管理列表 */
    getRoomList() {
      this.loading = true;
      listRmRooms().then(response => {
        this.RmRoomList = response.rows;
        // this.total = response.total;
        this.loading = false;
      });
    },
    selectRoom(val) {
      // const RoomMode = this.RmRoomList.find(item => item.uuid === val)
      // console.log("this.form.roomName===", this.form.roomName);
      //重新选择机房，自动更新所属机柜

      // const RoomMode="";
      getRmRoom(val).then(response => {
        const RoomMode = response.data;
        if (this.form.roomName != RoomMode.roomName || this.form.roomName == null) {
          this.form.deptId = "";
          // this.form.equipmentId = "";
        }
        this.form.roomName = RoomMode.roomName
        this.form.roomId = RoomMode.uuid
        this.form.deptId = RoomMode.deptId;
        this.getEqumentList(RoomMode.uuid);
      });
    },

    /** 查询设备模板管理列表 */
    getModelList() {
      this.loading = true;
      listRmEquipmentBoardModels().then(response => {
        this.RmEquipmentBoardModelList = response.rows;
        this.loading = false;
      });
    },
    selectModel(val) {
      // const res = this.RmEquipmentBoardModelList.find(item => item.uuid === val)
      getRmEquipmentBoardModel(val).then(response => {
        this.rmEquipmentPortList = response.data.rmEquipmentPortModelList;
        this.rmEquipmentPortList.forEach(item => {
          item.boardName = this.form.boardNumber;
        });
        this.form.status = response.data.status
        this.form.boardName = response.data.boardName
        // this.form.boardNumber = response.data.boardNumber
        this.form.model = response.data.model
        this.form.brand = response.data.brand
        this.form.length = response.data.length
        this.form.width = response.data.width
        this.form.height = response.data.height
        this.form.modelId = response.data.uuid
        this.form.modelName = response.data.boardName
      });
    },
    /** 查询机房管理列表 */
    getEqumentList(id) {
      this.loading = true;
      this.queryParams.roomId = id;
      listRmEquipments(this.EqumentqueryParams).then(response => {
        this.RmEquipmentList = response.rows;
        this.loading = false;
      });
    },
    selectEqument(val) {
      getRmEquipment(val).then(response => {
        const Equipment = response.data;
        this.rmEquipmentSlotList = Equipment.rmEquipmentSlotList;
        this.form.equipmentName = Equipment.equipmentName
        this.form.equipmentId = Equipment.uuid
        this.form.deptId = Equipment.deptId;
      });
    },
    selectEqumentSlot(val) {
      const EqumentSlot = this.rmEquipmentSlotList.find(item => item.uuid === val)
      // this.form.boardNumber = EqumentSlot.slotNumber
      this.form.slotId = EqumentSlot.uuid
      this.form.slotNumber = EqumentSlot.slotNumber
    },
    selectboardNumber() {
      this.rmEquipmentPortList.forEach(item => {
        item.boardName = this.form.boardNumber;
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.content {
  min-height: 70vh;
}
</style>
