import * as THREE from 'three';

/**
 * 创建机房走向架模型
 * @param {Array} RmRoomList - 机房信息列表
 * @returns {THREE.Group} - 走向架模型组
 */
export function initZouxiangjia(RmRoomList) {
    // 获取机房尺寸
    const roomLength = RmRoomList[0].length; // 机房长度 14.916m
    const roomWidth = Number(RmRoomList[0].width); // 机房宽度 8.3m
    const roomHeight = RmRoomList[0].height; // 机房高度 4m

    // 创建一个组来存放所有走向架
    const group = new THREE.Group();
    group.name = '机房走向架';

    // 走向架参数 - 统一参数便于管理
    const zouxiangjia = {
        width: 0.4,           // 走向架宽度为1m
        height: 0.2,       // 走向架高度
        length: 6,  // 走向架长度等于机房宽度
        spacing: 2,         // 走向架间距为2m
        gridSize: 0.2,      // 网格大小
        startPosition: 2    // 第一个走向架的起始位置，从2m开始，避免突出墙体
    };

    // 计算需要的走向架数量 - 从startPosition开始计算
    const zouxiangjiaCount = Math.floor((roomLength - zouxiangjia.startPosition) / zouxiangjia.spacing) + 1;

    // 创建走向架材质 - 使用更接近图片中的青色
    const zouxiangjiaMaterial = new THREE.MeshPhysicalMaterial({
        color: 0x00FFFF, // 青色
        metalness: 0.3,
        roughness: 0.4,
        clearcoat: 0.8,
        clearcoatRoughness: 0.2,
        transparent: true,
        opacity: 0.6, // 更透明一些
        side: THREE.DoubleSide,
        emissive: 0x00FFFF, // 添加自发光效果
        emissiveIntensity: 0.2 // 自发光强度
    });

    // 创建走向架基础几何体
    const zouxiangjiaGeometry = new THREE.BoxGeometry(zouxiangjia.length, zouxiangjia.height, zouxiangjia.width);

    // 创建多个走向架并设置位置
    for (let i = 0; i < zouxiangjiaCount; i++) {
        // 创建一个组来存放单个走向架及其网格结构
        const zouxiangjiaGroup = new THREE.Group();

        // 创建主体走向架
        const zouxiangjiaMesh = new THREE.Mesh(zouxiangjiaGeometry, zouxiangjiaMaterial);
        zouxiangjiaGroup.add(zouxiangjiaMesh);

        // 添加网格结构
        addGridStructure(zouxiangjiaGroup, zouxiangjia.length, zouxiangjia.width, zouxiangjia.gridSize);

        // 沿z轴摆放，从startPosition开始，每隔spacing放置一个
        const positionZ = zouxiangjia.startPosition + (i * zouxiangjia.spacing);

        // 调整位置使走向架位于机房内部
        zouxiangjiaGroup.position.set(
            (zouxiangjia.length / 2)+1, // x轴位置（居中）
            roomHeight, // y轴位置（在机房高度一半的位置上方0.5m）
            positionZ // z轴位置
        );

        // 添加到组中
        group.add(zouxiangjiaGroup);
    }

    // 添加支撑柱
    addSupportColumns(group, zouxiangjiaCount, zouxiangjia, roomWidth, roomHeight);

    return group;
}

/**
 * 添加网格结构到走向架
 * @param {THREE.Group} group - 走向架组
 * @param {Number} length - 走向架长度
 * @param {Number} width - 走向架宽度
 * @param {Number} gridSize - 网格大小
 */
function addGridStructure(group, length, width, gridSize) {
    // 创建网格材质 - 使用细线
    const gridMaterial = new THREE.MeshBasicMaterial({
        color: 0x00FFFF, // 青色
        transparent: true,
        opacity: 0.8
    });

    // 计算网格数量
    const gridCountX = Math.floor(length / gridSize);
    const gridCountZ = Math.floor(width / gridSize);

    // 创建水平方向的网格线（沿X轴）
    for (let i = 1; i <= gridCountZ; i++) {
        const gridLineGeometry = new THREE.BoxGeometry(length, 0.01, 0.01);
        const gridLine = new THREE.Mesh(gridLineGeometry, gridMaterial);

        // 设置网格线位置
        gridLine.position.set(
            0, // 相对于走向架的中心
            0.03, // 稍微高于走向架表面
            -width / 2 + i * (width / gridCountZ) // Z轴位置
        );

        // group.add(gridLine);
    }

    // 创建垂直方向的网格线（沿Z轴）
    for (let i = 0; i <= gridCountX; i++) {
        const gridLineGeometry = new THREE.BoxGeometry(0.01, 0.01, width);
        const gridLine = new THREE.Mesh(gridLineGeometry, gridMaterial);

        // 设置网格线位置
        gridLine.position.set(
            -length / 2 + i * (length / gridCountX), // X轴位置
            0.03, // 稍微高于走向架表面
            0 // 相对于走向架的中心
        );

        // group.add(gridLine);
    }
}

/**
 * 添加走向架的支撑柱
 * @param {THREE.Group} group - 走向架组
 * @param {Number} count - 走向架数量
 * @param {Object} zouxiangjia - 走向架参数对象
 * @param {Number} roomWidth - 机房宽度
 * @param {Number} roomHeight - 机房高度
 */
function addSupportColumns(group, count, zouxiangjia, roomWidth, height) {
    // 支撑柱参数
    const columnRadius = 0.03; // 支撑柱半径，更细一些
    const columnHeight = height; // 支撑柱高度，从地面到天花板

    // 创建支撑柱材质
    const columnMaterial = new THREE.MeshPhysicalMaterial({
        color: 0xA0A0A0, // 银灰色
        metalness: 0.9,
        roughness: 0.1,
        clearcoat: 1.0,
        clearcoatRoughness: 0.1
    });

    // 创建支撑柱几何体
    const columnGeometry = new THREE.CylinderGeometry(columnRadius, columnRadius, columnHeight, 8);

    // 为每个走向架添加支撑柱
    for (let i = 0; i < count; i++) {
        // 每个走向架的z位置，从startPosition开始
        const positionZ = zouxiangjia.startPosition + (i * zouxiangjia.spacing);

        // 在走向架两端各添加一个支撑柱，并在中间添加额外的支撑柱
        const supportPoints = [0, roomWidth / 3, 2 * roomWidth / 3, roomWidth];

        for (let j = 0; j < supportPoints.length; j++) {
            const column = new THREE.Mesh(columnGeometry, columnMaterial);

            // 设置支撑柱位置
            column.position.set(
                supportPoints[j], // x轴位置
                height / 2, // y轴位置（从地面到天花板中间）
                positionZ // z轴位置
            );

            // 添加到组中
            // group.add(column);
        }
    }

    // 添加横向连接支撑
    addHorizontalSupports(group, count, zouxiangjia, roomWidth, height);
}

/**
 * 添加横向连接支撑
 * @param {THREE.Group} group - 走向架组
 * @param {Number} count - 走向架数量
 * @param {Object} zouxiangjia - 走向架参数对象
 * @param {Number} roomWidth - 机房宽度
 * @param {Number} height - 机房高度
 */
function addHorizontalSupports(group, count, zouxiangjia, roomWidth, height) {
    // 横向支撑参数
    const supportRadius = 0.02; // 支撑半径，比主柱更细

    // 创建横向支撑材质
    const supportMaterial = new THREE.MeshPhysicalMaterial({
        color: 0xA0A0A0, // 银灰色
        metalness: 0.9,
        roughness: 0.1,
        clearcoat: 1.0,
        clearcoatRoughness: 0.1
    });

    // 添加沿X轴的横向支撑（连接左右两侧的柱子）
    for (let i = 0; i < count; i++) {
        // 从startPosition开始计算位置
        const positionZ = zouxiangjia.startPosition + (i * zouxiangjia.spacing);

        // 在不同高度添加横向支撑
        const heightPositions = [
            height / 4,           // 1/4高度
            height / 2 + 0.5,     // 走向架位置
            3 * height / 4        // 3/4高度
        ];

        for (let h = 0; h < heightPositions.length; h++) {
            // 创建横向支撑几何体
            const horizontalGeometry = new THREE.CylinderGeometry(supportRadius, supportRadius, roomWidth, 8);
            horizontalGeometry.rotateZ(Math.PI / 2); // 旋转使其沿X轴方向

            const horizontalSupport = new THREE.Mesh(horizontalGeometry, supportMaterial);

            // 设置横向支撑位置
            horizontalSupport.position.set(
                roomWidth / 2, // x轴位置（居中）
                heightPositions[h], // y轴位置
                positionZ // z轴位置
            );

            // 添加到组中
            // group.add(horizontalSupport);
        }
    }

    // 添加沿Z轴的横向支撑（连接前后的走向架）
    if (count > 1) {
        // 支撑点位置
        const supportPoints = [0, roomWidth / 3, 2 * roomWidth / 3, roomWidth];

        for (let j = 0; j < supportPoints.length; j++) {
            // 创建横向支撑几何体 - 长度需要考虑startPosition
            const supportLength = (count - 1) * zouxiangjia.spacing;
            const horizontalGeometry = new THREE.CylinderGeometry(supportRadius, supportRadius, supportLength, 8);
            horizontalGeometry.rotateX(Math.PI / 2); // 旋转使其沿Z轴方向

            const horizontalSupport = new THREE.Mesh(horizontalGeometry, supportMaterial);

            // 设置横向支撑位置 - 需要考虑startPosition
            horizontalSupport.position.set(
                supportPoints[j], // x轴位置
                height / 2 + 0.5, // y轴位置（走向架位置）
                zouxiangjia.startPosition + supportLength / 2 // z轴位置（考虑startPosition后居中）
            );

            // 添加到组中
            // group.add(horizontalSupport);
        }
    }
}
