<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>
<script>
import echarts from "echarts";
require("echarts/theme/macarons"); // echarts theme
import resize from "./mixins/resize";

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: "chart"
    },
    width: {
      type: String,
      default: "100%"
    },
    seriesName: {
      type: String,
      default: "故障原因"
    },
    height: {
      type: String,
      default: "300px"
    },
    pieData: {
      type: Array,
      required: true
    }
  },
  data() {
    return {
      chart: null
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  },
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.dispose();
    this.chart = null;
  },
  watch: {
    pieData: {
      deep: true,
      handler() {
        this.initChart();
      }
    },
    seriesName: {
      deep: true,
      handler() {
        this.initChart();
      }
    }
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el, "macarons");
      this.chart.setOption({
        tooltip: {
          trigger: "item",
          formatter: "{a} <br/>{b} : {c} ({d}%)"
        },
        legend: {
          left: "center",
          bottom: "10",
          textStyle:{
            fontSize: 12,//字体大小
            color: '#ffffff'//字体颜色
          }
        },
        series: [
          {
            name: this.seriesName,
            type: "pie",
            roseType: "radius",
            radius: [15, 95],
            center: ["50%", "48%"],
            itemStyle: {
              normal: {
                label: {
                  show: true,
                  formatter: "{c}"
                },
                labelLine: { show: true }
              }
            },
            data: this.pieData,
            animationEasing: "cubicInOut",
            animationDuration: 2600
          }
        ]
      });
    }
  }
};
</script>
