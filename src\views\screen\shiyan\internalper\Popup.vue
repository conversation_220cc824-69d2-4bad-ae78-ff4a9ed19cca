<template>
  <div class="server-popup" v-if="showPopup" style="z-index: 1001;">
    <div class="popup-title">{{ `U-${currentClickedNumber}` }}</div>
    <div class="server-info" v-if="ServerState || !isOccupiedPosition"> <!-- 新增条件判断 -->
      <div class="custom-select" @click="toggleDropdown">
        <div class="selected-model">{{ selectedEquipmentType ? getEquipmentTypeLabel(selectedEquipmentType) : '请选择' }}</div>
        <ul class="dropdown-list" v-if="isOpen">
          <li v-if="loading" class="loading-item">加载中...</li>
          <div v-else>
            <!-- 固定显示三种设备类型 -->
            <li v-for="type in fixedEquipmentTypes"
                :key="type.value"
                @click.stop="selectEquipmentType(type.value)"
                :class="{ 'selected': selectedEquipmentType === type.value }">
                {{ type.label }}
            </li>
          </div>
        </ul>
      </div>
    </div>
    <button v-if="ServerState && selectedEquipmentType" class="action-button" @click="onShowAddEquipment">上传{{ getEquipmentTypeLabel(selectedEquipmentType) }}</button>
    <button v-if="!ServerState && isOccupiedPosition" class="action-button remove-button" @click.stop="onRemoveServer">下架服务器</button>

    <!-- 添加设备组件 -->
    <AddEquipment
      :visible.sync="showAddEquipment"
      :selectedCabinetId="selectedCabinetId"
      :currentClickedNumber="currentClickedNumber"
      :selectedEquipmentType="selectedEquipmentType"
      :equipmentList="RmEquipmentList"
      :cabinetTotalU="buttonCount"
      @update-equipment-list="handleUpdateEquipmentList"
      @update:visible="handleAddEquipmentVisibleChange"
    />
  </div>
</template>

<script>
import { generateUUID } from 'three/src/math/MathUtils.js';
import { delRmEquipment, listRmEquipment } from "@/api/equipment/RmEquipment";
import { listRmEquipmentModel } from "@/api/model/RmEquipmentModel";
import { getDicts } from "@/api/system/dict/data";
import AddEquipment from "./AddEquipment.vue";

export default {
  name: 'ServerPopup',
  components: {
    AddEquipment
  },
  props: {
    showPopup: {
      type: Boolean,
      default: false
    },
    RmEquipmentList: {
      type: Array,
      required: true
    },
    buttonCount:{
      type: Number,
    },
    currentMouseNumber:{
      type: Number,
    },
    Motor2parameters:{
      type: Object,
    },
    currentClickedNumber:{
      type: Number,
    },
    ServerState:{
      type: Boolean,
      default: false
    },
    selectedCabinetId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      selectedModel: null, // 初始化为 null
      selectedEquipmentType: null, // 选中的设备类型
      isOpen: false,
      currentEquipment: null, // 存储当前位置的设备信息
      showAddEquipment: false, // 控制AddEquipment组件的显示
      form: {
        uuid: generateUUID(), // 生成新的UUID
        modelName: "",
        roomName: "",
        cabinetName: "",
        equipmentName: "",
        equipmentCode: "",
        equipmentType: "",
        model: "",
        manufacturer: "",
        length: "",
        width: "",
        height: "",
        startU: "",
        occupiedU: "",
        status: "1",
        modelId: generateUUID(),
        roomId: "",
        brand: "",
        cabinetId: "", // 将在onShangJiaServer中设置
        deptId: "",
        ipAddress: "",
        rmRoom: null
      },
      // 固定的设备类型列表
      fixedEquipmentTypes: [
        { label: '服务器', value: '1' },
        { label: '交换机', value: '2' },
        { label: '接入设备', value: '3' }
      ],
      equipmentTypes: [],
      equipmentModels: [], // 存储从后端获取的设备模型数据
      occupiedUList: [], // 存储所有设备的occupiedU
      startUList: [], // 存储所有设备的startU
      loading: false, // 加载状态
      queryParams: {
        pageNum: 1,
        pageSize: 100, // 获取足够多的数据
      },
      dictOptions: [], // 存储字典数据
    };
  },
  computed: {
    // 判断当前位置是否已被占用
    isOccupiedPosition() {
      if (!this.currentClickedNumber || !this.RmEquipmentList || this.RmEquipmentList.length === 0) {
        return false;
      }

      // 查找当前位置是否有设备
      for (const equipment of this.RmEquipmentList) {
        const startU = parseInt(equipment.startU);
        const occupiedU = parseInt(equipment.occupiedU);

        // 如果当前点击的位置在某个设备的占用范围内
        if (this.currentClickedNumber >= startU && this.currentClickedNumber < startU + occupiedU) {
          // 保存找到的设备信息
          this.currentEquipment = equipment;
          return true;
        }
      }

      this.currentEquipment = null;
      return false;
    }
  },
  created() {
    // 初始化时获取设备模型数据和字典数据
    this.getEquipmentModels();
    this.getEquipmentData();
    this.getDictData();

    // 同时处理传入的RmEquipmentList数据
    if (this.RmEquipmentList && this.RmEquipmentList.length > 0) {
      // 提取不重复的设备类型
      const uniqueTypes = [...new Set(this.RmEquipmentList.map(item => item.equipmentType))];
      this.equipmentTypes = uniqueTypes;

      // 提取所有设备的occupiedU和startU
      this.occupiedUList = this.RmEquipmentList.map(item => item.occupiedU);
      this.startUList = this.RmEquipmentList.map(item => item.startU);
    }
  },
  methods: {
    // 获取设备类型标签
    getEquipmentTypeLabel(value) {
      const type = this.fixedEquipmentTypes.find(item => item.value === value);
      return type ? type.label : '请选择';
    },

    // 选择设备类型
    selectEquipmentType(value) {
      this.selectedEquipmentType = value;
      this.isOpen = false;
    },

    // 显示添加设备组件
    onShowAddEquipment() {
      this.showAddEquipment = true;
    },

    // 处理设备列表更新
    handleUpdateEquipmentList(data) {
      this.$emit('update:show-popup', false);
      this.$emit('update-RmEquipmentList', data);
      this.getEquipmentData();
      this.resetForm();
    },

    // 处理AddEquipment组件可见性变化
    handleAddEquipmentVisibleChange(visible) {
      // 当AddEquipment组件关闭时，关闭Popup框
      if (!visible) {
        this.showAddEquipment = false;
        // 重置选中的设备类型
        this.selectedEquipmentType = null;
        // 如果是通过取消按钮关闭的，也关闭Popup框
        this.$emit('update:show-popup', false);
      }
    },

    // 获取设备模型数据
    getEquipmentModels() {
      this.loading = true;
      listRmEquipmentModel(this.queryParams).then(response => {
        this.equipmentModels = response.rows;

        // 提取不重复的设备类型
        if (this.equipmentModels && this.equipmentModels.length > 0) {
          const uniqueTypes = [...new Set(this.equipmentModels.map(item => item.equipmentType))];
          // 合并从RmEquipmentList和equipmentModels中获取的类型
          this.equipmentTypes = [...new Set([...this.equipmentTypes, ...uniqueTypes])];

          // 提取所有设备的occupiedU
          const occupiedUValues = this.equipmentModels.map(item => item.occupiedU);
          this.occupiedUList = [...new Set([...this.occupiedUList, ...occupiedUValues])];
        }

        this.loading = false;
      }).catch(error => {
        console.error('获取设备模型数据失败:', error);
        this.loading = false;
      });
    },

    // 获取设备数据
    getEquipmentData() {
      this.loading = true;
      listRmEquipment(this.queryParams).then(response => {
        const equipmentData = response.rows;

        // 提取不重复的设备类型
        if (equipmentData && equipmentData.length > 0) {
          const uniqueTypes = [...new Set(equipmentData.map(item => item.equipmentType))];
          // 合并已有的类型
          this.equipmentTypes = [...new Set([...this.equipmentTypes, ...uniqueTypes])];

          // 提取所有设备的occupiedU和startU
          const occupiedUValues = equipmentData.map(item => item.occupiedU);
          const startUValues = equipmentData.map(item => item.startU);

          this.occupiedUList = [...new Set([...this.occupiedUList, ...occupiedUValues])];
          this.startUList = [...new Set([...this.startUList, ...startUValues])];
        }

        this.loading = false;
      }).catch(error => {
        console.error('获取设备数据失败:', error);
        this.loading = false;
      });
    },

    // 获取字典数据
    getDictData() {
      getDicts('equipment_type').then(response => {
        this.dictOptions = response.data;
      }).catch(error => {
        console.error('获取字典数据失败:', error);
      });
    },

    toggleDropdown() {
      this.isOpen = !this.isOpen;
    },
    selectModel(model) {
      // console.log('选择了模型:', model.equipmentName);
      this.selectedModel = model;
      this.isOpen = false;

      // 将选中的模型数据赋值给form对象
      if (model) {
        // 复制模型的所有属性到form对象
        Object.keys(model).forEach(key => {
          this.form[key] = model[key];
        });

        // 确保保留必要的字段，如modelId, uuid等
        this.form.equipmentName = model.equipmentName;
        this.form.equipmentCode = generateUUID();
        this.form.equipmentType = model.equipmentType;
        this.form.model = model.model;
        this.form.brand = model.brand || model.manufacturer;
        this.form.manufacturer = model.manufacturer;
        this.form.length = model.length;
        this.form.width = model.width;
        this.form.height = model.height;
        this.form.occupiedU = model.occupiedU;
        this.form.status = "1"; // 设置状态为可用

        // 生成新的UUID，避免与原模型冲突
        this.form.modelId = generateUUID();
      }
    },
    // 检查是否有足够的空间放置设备
    checkSpaceAvailability(startU, occupiedU) {
      // 如果没有设备列表或者没有选中机柜，直接返回true
      if (!this.RmEquipmentList || this.RmEquipmentList.length === 0 || !this.$parent.selectedCabinet) {
        return true;
      }

      // 获取机柜的总U数
      const totalU = this.buttonCount || 42; // 默认值为42U，如果没有获取到buttonCount

      // 检查是否超出机柜总高度
      if (parseInt(startU) + parseInt(occupiedU) > totalU) {
        return false;
      }

      // 筛选出当前机柜的设备
      const currentCabinetEquipments = this.RmEquipmentList.filter(
        (item) => item.cabinetId === this.selectedCabinetId
      );

      // 检查是否与现有设备重叠
      for (const equipment of currentCabinetEquipments) {
        const equipStartU = parseInt(equipment.startU);
        const equipOccupiedU = parseInt(equipment.occupiedU);

        // 检查是否有重叠
        // 情况1：新设备的起始位置在现有设备的范围内
        // 情况2：新设备的结束位置在现有设备的范围内
        // 情况3：新设备完全包含现有设备
        if (
          (startU >= equipStartU && startU < equipStartU + equipOccupiedU) ||
          (startU + occupiedU > equipStartU && startU + occupiedU <= equipStartU + equipOccupiedU) ||
          (startU <= equipStartU && startU + occupiedU >= equipStartU + equipOccupiedU)
        ) {
          return false;
        }
      }

      return true;
    },

    // 此方法已被onShowAddEquipment替代
    onShangJiaServer() {
      // 保留此方法以防其他地方调用
      this.onShowAddEquipment();
    },



    // 下架服务器方法
    onRemoveServer() {
      // 确保有设备可以下架
      if (!this.currentEquipment || !this.currentEquipment.uuid) {
        this.$modal.msgError("未找到可下架的设备");
        return;
      }

      // 显示确认对话框
        const loadingInstance = this.$loading({
          lock: true,
          text: '处理中...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        // 调用删除API
        delRmEquipment(this.currentEquipment.uuid).then(() => {
          loadingInstance.close();

          // 通知父组件更新设备列表和相关数组
          this.$emit('update:show-popup', false);
          this.$emit('update-RmEquipmentList', {
            equipmentId: this.currentEquipment.uuid,
            startU: parseInt(this.currentEquipment.startU),
            occupiedU: parseInt(this.currentEquipment.occupiedU),
            removed: true // 标记为移除操作
          });

          // 重新获取最新的设备数据
          this.getEquipmentData();
          this.$modal.msgSuccess("服务器已成功下架");

          // 重置当前设备信息
          this.currentEquipment = null;
        }).catch(error => {
          loadingInstance.close();
          console.error('下架服务器失败:', error);
          this.$modal.msgError("下架失败，请重试");
        });
    },

    // 重置表单
    resetForm() {
      this.form = {
        uuid: generateUUID(),
        modelName: "",
        roomName: "",
        cabinetName: "",
        equipmentName: "",
        equipmentCode: "",
        equipmentType: "",
        model: "",
        manufacturer: "",
        length: "",
        width: "",
        height: "",
        startU: "",
        occupiedU: "",
        status: "1",
        modelId: generateUUID(),
        roomId: "",
        brand: "",
        cabinetId: "",
        deptId: "",
        ipAddress: "",
        rmRoom: null
      };
      this.selectedModel = null;
      this.selectedEquipmentType = null;
      this.showAddEquipment = false;
      // 通知父组件关闭Popup
      this.$emit('update:show-popup', false);
    }
  },
}
</script>

<style scoped>
.server-popup {
  background-color: #003a70; /* 主背景色 */
  padding: 12px;
  border-radius: 6px;
  width: 200px; /* 可根据需要调整宽度 */
  position: absolute;
  transform: translateX(30px);
  z-index: 1;
}

.popup-title {
  color: #ffffff;
  font-size: 16px;
  margin-bottom: 10px;
}

.server-info {
  background-color: #0050a3; /* 信息区背景色 */
  padding: 10px;
  margin-bottom: 12px;
  color: #ffffff;
  font-size: 14px;
  display: flex; /* 方便布局下拉框 */
  align-items: center;
}

.custom-select {
  position: relative;
  width: 100%;
  cursor: pointer;
}

.selected-model {
  background-color: #0050a3; /* 下拉框背景色 */
  color: #ffffff; /* 文字颜色 */
  border: 1px solid #007aff; /* 边框颜色 */
  padding: 6px;
  border-radius: 4px;
  width: 100%; /* 撑满父容器宽度 */
  box-sizing: border-box;
}

.dropdown-list {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: #0050a3; /* 下拉框背景色 */
  color: #ffffff; /* 文字颜色 */
  border: 1px solid #007aff; /* 边框颜色 */
  border-top: none;
  border-radius: 0 0 4px 4px;
  max-height: 300px; /* 10 条数据 * 16px（每条数据的高度） */
  overflow-y: auto;
  z-index: 1001;
  padding: 0;
  margin: 0;
  list-style: none;
}

.dropdown-list li {
  padding: 6px;
  cursor: pointer;
}

.action-button {
  width: 100%;
  background-color: #007aff; /* 按钮背景色 */
  color: #ffffff;
  border: none;
  padding: 10px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.dropdown-list li:hover,
.dropdown-list li.selected {
  background-color: #007aff; /* 选中背景色 */
}

.loading-item {
  text-align: center;
  padding: 10px;
  color: #ffffff;
  font-style: italic;
}

.model-info {
  font-size: 12px;
  color: #a0d0ff;
  margin-left: 5px;
}

.remove-button {
  background-color: #ff3b30; /* 红色背景，表示下架操作 */
}

.remove-button:hover {
  background-color: #ff6b6b; /* 悬停时的颜色 */
  box-shadow: 0 4px 8px rgba(255, 59, 48, 0.4);
}
</style>