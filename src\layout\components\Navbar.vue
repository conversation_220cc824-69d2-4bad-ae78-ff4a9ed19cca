<template>
  <div class="navbar">
    <hamburger id="hamburger-container" :is-active="sidebar.opened" class="hamburger-container" @toggleClick="toggleSideBar" />

    <breadcrumb id="breadcrumb-container" class="breadcrumb-container" v-if="!topNav"/>
    <top-nav id="topmenu-container" class="topmenu-container" v-if="topNav"/>

    <div class="right-menu">
      <template v-if="device!=='mobile'">
        <screenfull id="screenfull" class="right-menu-item hover-effect" />
        <el-tooltip content="布局大小" effect="dark" placement="bottom">
          <size-select id="size-select" class="right-menu-item hover-effect" />
        </el-tooltip>
      </template>
      <el-dropdown class="avatar-container right-menu-item hover-effect" trigger="click">
        <div class="avatar-wrapper">
          <img :src="avatar" class="user-avatar">
          <i class="el-icon-caret-bottom" />
           <span class="user_name">{{nickName}}</span>
        </div>
        <el-dropdown-menu slot="dropdown">
          <router-link to="/user/profile">
            <el-dropdown-item>个人中心</el-dropdown-item>
          </router-link>
          <el-dropdown-item @click.native="setting = true">
            <span>布局设置</span>
          </el-dropdown-item>
          <el-dropdown-item divided @click.native="logout">
            <span>退出登录</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>

    <el-dialog :title="titles" :visible.sync="opens" width="500px" append-to-body :close-on-click-modal="false" :show-close="false">
      <template slot="title">
        <div class="avue-crud__dialog__header" style="border-bottom:1px solid #DCDFE6;padding-bottom:15px;">
          <span class="el-dialog__title">
            <span
              style="display:inline-block;background-color: #3478f5;width:3px;height:20px;margin-right:5px; float: left;margin-top:2px;margin-right:15px"></span>
            {{titles}}
          </span>
        </div>
      </template>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
          <el-form-item label="旧密码" prop="oldPassword">
            <el-input v-model="form.oldPassword" placeholder="请输入旧密码" type="password" show-password/>
          </el-form-item>
          <el-form-item label="新密码" prop="newPassword">
          <el-input v-model="form.newPassword" placeholder="请输入新密码" type="password" show-password/>
          </el-form-item>
          <el-form-item label="确认密码" prop="confirmPassword">
            <el-input v-model="form.confirmPassword" placeholder="请确认新密码" type="password" show-password/>
          </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submit">提 交</el-button>
        <el-button type="warning" divided @click.native="logout">退出登录</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Breadcrumb from '@/components/Breadcrumb'
import TopNav from '@/components/TopNav'
import Hamburger from '@/components/Hamburger'
import Screenfull from '@/components/Screenfull'
import SizeSelect from '@/components/SizeSelect'
import Search from '@/components/HeaderSearch'
// import RuoYiGit from '@/components/RuoYi/Git'
// import RuoYiDoc from '@/components/RuoYi/Doc'

import { getUserProfile,updateUserPwd } from "@/api/system/user";
import { login, logout, getInfo,getPublicKey } from '@/api/login'
import { encrypt } from '@/utils/jsencrypt'
export default {
  components: {
    Breadcrumb,
    TopNav,
    Hamburger,
    Screenfull,
    SizeSelect,
    Search,
  },
   data() {
    const equalToPassword = (rule, value, callback) => {
      if (this.form.newPassword !== value) {
        callback(new Error("两次输入的密码不一致"));
      } else if(this.form.newPassword==this.initPassword){
          callback(new Error("修改密码不可以为默认密码"));
      }else {
        callback();
      }
    };
       return {
         nickName:null,
          opens:false,
      titles:"重置密码",
      form:{
        onlogin:null,
        oldPassword:null,
        newPassword:null,
        confirmPassword:null

      },
         // 表单校验
      rules: {
        oldPassword: [
          { required: true, message: "旧密码不能为空", trigger: "blur" }
        ],
        newPassword: [
         { required: true, message: "确认密码不能为空", trigger: "blur" },
          { pattern: /^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[!@#$%^&,.*]).{8,16}$/, message: '请输入8-16位，由大小写字母、数字和特殊符号组合的密码' },
          { required: true, message: "用户密码不能为空", trigger: "blur" },
          { min: 8, max: 16, message: '用户密码长度必须介于 8 和 16 之间', trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, message: "确认密码不能为空", trigger: "blur" },
          { required: true, validator: equalToPassword, trigger: "blur" },
          { pattern: /^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[!@#$%^&,.*]).{8,16}$/, message: '请输入8-16位，由大小写字母、数字和特殊符号组合的密码' },
          { required: true, message: "用户密码不能为空", trigger: "blur" },
          { min: 8, max: 16, message: '用户密码长度必须介于 8 和 16 之间', trigger: 'blur' }
        ]
      },
      initPassword:null,
    }
  },
  computed: {
    ...mapGetters([
      'sidebar',
      'avatar',
      'device'
    ]),
    setting: {
      get() {
        return this.$store.state.settings.showSettings
      },
      set(val) {
        this.$store.dispatch('settings/changeSetting', {
          key: 'showSettings',
          value: val
        })
      }
    },
    topNav: {
      get() {
        return this.$store.state.settings.topNav
      }
    }
  },
    created() {
    this.getUser();
    // this.getPublicKey();
  },
  methods: {
     async logout() {
      this.$store.dispatch('LogOut').then(() => {
          location.href = '/index';
      })
    },
    getUser() {
      getUserProfile().then(response => {
        this.user = response.data;
        this.nickName=this.user.nickName;
       if(this.user.oneLogin=="Y"){
        this.getConfigKey("sys.user.initPassword").then(response => {
          this.initPassword = response.msg;
        });
         this.opens=true; 
         this.getPublicKey();
       }
      });
    },
     getPublicKey() {
      return new Promise((resolve, reject) => {
        getPublicKey()
          .then(res => {
            resolve(res)
          })
          .catch(error => {
            reject(error)
          })
      })
    },
      submit() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.getPublicKey().then(res=>{
            let publicKey = res.publicKey
            const oldPassword = encrypt(this.form.oldPassword, publicKey)
            const newPassword = encrypt(this.form.newPassword, publicKey)
            updateUserPwd(oldPassword, newPassword).then(
              response => {
                this.$modal.msgSuccess("修改成功");
                this.$store.dispatch('LogOut').then(() => {
                    location.href = '/index';
                })
                this.opens=false; 
              }
            );
          })
        }
      });
    },
    toggleSideBar() {
      this.$store.dispatch('app/toggleSideBar')
    },
    async logout() {
      this.$confirm('确定注销并退出系统吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$store.dispatch('LogOut').then(() => {
          location.href = '/index';
        })
      }).catch(() => {});
    }
  }
}
</script>

<style lang="scss" scoped>
.navbar {
  height: 50px;
  overflow: hidden;
  position: relative;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0,21,41,.08);

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background .3s;
    -webkit-tap-highlight-color:transparent;

    &:hover {
      background: rgba(0, 0, 0, .025)
    }
  }

  .breadcrumb-container {
    float: left;
  }

  .topmenu-container {
    position: absolute;
    left: 50px;
  }

  .errLog-container {
    display: inline-block;
    vertical-align: top;
  }

  .right-menu {
    float: right;
    height: 100%;
    line-height: 50px;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background .3s;

        &:hover {
          background: rgba(0, 0, 0, .025)
        }
      }
    }

    .avatar-container {
      margin-right: 30px;

      .avatar-wrapper {
        margin-top: 5px;
        position: relative;
        display: flex;  /* 设置父元素为flex布局 */
        align-items: center;  /* 垂直居中 */
        justify-content: center; /* 水平居中 */

        .user-avatar {
          cursor: pointer;
          width: 40px;
          height: 40px;
          border-radius: 10px;
        }

        .el-icon-caret-bottom {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 25px;
          font-size: 12px;
        }
        .user_name{
          margin-left:10px; 
        }

      }
    }
  }
}
</style>
