import * as THREE from 'three';
// const geometry = new THREE.PlaneGeometry(100,100);
// const texture = new THREE.TextureLoader().load('./pic/cizhuan.jpg');
// const material = new THREE.MeshBasicMaterial({
//     map: texture,
//     side: THREE.DoubleSide
// });
// const plane = new THREE.Mesh(geometry, material);
// texture.wrapS = THREE.RepeatWrapping;
// texture.wrapT = THREE.RepeatWrapping;
// texture.repeat.set(20, 20);
// plane.rotateX(Math.PI / 2);
// export default plane;
const geometry = new THREE.PlaneGeometry(10,10);
const TextureLoader = new THREE.TextureLoader();
const texture = TextureLoader.load('./pic/zhuanwan.png');
texture.offset.x=0.5;
texture.wrapS = THREE.RepeatWrapping;
const material = new THREE.MeshBasicMaterial({
    map: texture,
    transparent: true,
});
const plane = new THREE.Mesh(geometry, material);
plane.rotateX(-Math.PI / 2);
// const renderer = new THREE.WebGLRenderer();

// function render(){
//     texture.offset.x += 0.001;
//     renderer.render(scene, camera);
//     requestAnimationFrame(render);
// }
// render();

export default plane;