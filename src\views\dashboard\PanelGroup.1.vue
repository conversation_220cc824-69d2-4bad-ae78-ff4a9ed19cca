<template>
  <el-row :gutter="40" class="panel-group">
    <el-col :xs="12" :sm="12" :lg="12" class="card-panel-col">
      <div class="content_item">
        <c-card title="ICT列收">
          <el-row :gutter="24">
            <el-col :span="6">
              <div class="title" style="height: 160px;margin:20px 10px;">
                <el-progress type="circle" :stroke-width="24" :percentage=" this.panelData.ptlv"></el-progress>
              </div>
            </el-col>
            <el-col :span="18">
                <div style="height: 160px;margin:10px;">
                <el-row :gutter="24">
                    <el-col :span="12">
                      <div class="card-panel">
                        <div class="card-panel-icon-wrapper icon-people">
                            <svg-icon icon-class="example" class-name="card-panel-icon" />
                        </div>
                        <div class="card-panel-description">
                          <div class="card-panel-text">
                            年度目标
                          </div>
                            <span class="card-panel-num">{{ this.panelData.pttarget }}</span>
                        </div>
                      </div>
                    </el-col>
                    <el-col :span="12">
                       <div class="card-panel">
                        <div class="card-panel-icon-wrapper icon-shopping">
                            <svg-icon icon-class="chart" class-name="card-panel-icon" />
                        </div>
                        <div class="card-panel-description">
                          <div class="card-panel-text">
                            累计完成
                          </div>
                            <span class="card-panel-num">{{ this.panelData.ptcount }}</span>
                        </div>
                      </div>
                    </el-col>
                </el-row>
                  
                   <el-row :gutter="24">
                    <el-col :span="12">
                      <div class="card-panel">
                        <div class="card-panel-icon-wrapper icon-message">
                            <svg-icon icon-class="dashboard" class-name="card-panel-icon" />
                        </div>
                        <div class="card-panel-description">
                          <div class="card-panel-text">
                            当月列收
                          </div>
                            <span class="card-panel-num">{{ this.panelData.ptbymonthly }}</span>
                        </div>
                      </div>
                    </el-col>
                    <el-col :span="12">
                       <div class="card-panel">
                        <div class="card-panel-icon-wrapper icon-money">
                            <svg-icon icon-class="star" class-name="card-panel-icon" />
                        </div>
                        <div class="card-panel-description">
                          <div class="card-panel-text">
                            当前排名
                          </div>
                            <span class="card-panel-num">{{ this.panelData.ptranking }}</span>
                        </div>
                      </div>
                    </el-col>
                  </el-row>
                </div>
            </el-col>
          </el-row>
        </c-card>
      </div>
    </el-col>
    <el-col :xs="12" :sm="12" :lg="12" class="card-panel-col">
      <div class="content_item">
        <c-card title="ICT签约">
          <el-row :gutter="24">
            <el-col :span="6">
              <div class="title" style="height: 160px;margin:20px 10px;">
                <el-progress type="circle" :stroke-width="24" :percentage=" this.panelData.pslv"></el-progress>
              </div>
            </el-col>
            <el-col :span="18">
                <div style="height: 160px;margin:10px;">
                      <el-row :gutter="24">
                    <el-col :span="12">
                      <div class="card-panel">
                        <div class="card-panel-icon-wrapper icon-people">
                            <svg-icon icon-class="example" class-name="card-panel-icon" />
                        </div>
                        <div class="card-panel-description">
                          <div class="card-panel-text">
                            年度目标
                          </div>
                            <span class="card-panel-num">{{ this.panelData.pstarget }}</span>
                        </div>
                      </div>
                    </el-col>
                    <el-col :span="12">
                       <div class="card-panel">
                        <div class="card-panel-icon-wrapper icon-shopping">
                            <svg-icon icon-class="chart" class-name="card-panel-icon" />
                        </div>
                        <div class="card-panel-description">
                          <div class="card-panel-text">
                            累计完成
                          </div>
                            <span class="card-panel-num">{{ this.panelData.pscount }}</span>
                        </div>
                      </div>
                    </el-col>
                </el-row>
                   <el-row :gutter="24">
                    <el-col :span="12">
                      <div class="card-panel">
                        <div class="card-panel-icon-wrapper icon-message">
                            <svg-icon icon-class="dashboard" class-name="card-panel-icon" />
                        </div>
                        <div class="card-panel-description">
                          <div class="card-panel-text">
                            当月签约
                          </div>
                            <span class="card-panel-num">{{ this.panelData.psbymonthly }}</span>
                        </div>
                      </div>
                    </el-col>
                    <el-col :span="12">
                       <div class="card-panel">
                        <div class="card-panel-icon-wrapper icon-money">
                            <svg-icon icon-class="star" class-name="card-panel-icon" />
                        </div>
                        <div class="card-panel-description">
                          <div class="card-panel-text">
                            当前排名
                          </div>
                            <span class="card-panel-num">{{ this.panelData.psranking }}</span>
                        </div>
                      </div>
                    </el-col>
                  </el-row> 
                </div>
            </el-col>
          </el-row>
        </c-card>
      </div>
    </el-col>
  </el-row>
</template>

<script>
import CCard from '@/components/CCard'
export default {
  components: {
    CCard
  },
  props: {
    panelData: {
      type: Object,
      required: true
    },
  },
}
</script>

<style lang="scss" scoped>
.panel-group {
  margin-top: 0px;

  .card-panel-col {
    margin-bottom: 12px;
  }
  .content_item {
    height: 220px;
    cursor: pointer;
    font-size: 12px;
    position: relative;
    overflow: hidden;
    color: #666;
    background: #fff;
    box-shadow: 4px 4px 40px rgba(0, 0, 0, .05);
    border-color: rgba(0, 0, 0, .05);
  }
  .card-panel {
    width: 100%;
    height: 70px;
    box-shadow: 0 3px 11px 0 rgba(0, 0, 0, 0.10);
    border-radius: 5px;
    float: left;
    margin-bottom: 10px;
    text-align: center;

    &:hover {
      .card-panel-icon-wrapper {
        color: #fff;
      }

      .icon-people {
        background: #FF00FF;
      }

      .icon-message {
        background: #36a3f7;
      }

      .icon-money {
        background: #f4516c;
      }

      .icon-shopping {
        background: #34bfa3;
      }

       .icon-star {
        background: #f4516c;
      }
    }

    .icon-people {
      color: #FF00FF;
    }

    .icon-message {
      color: #36a3f7;
    }

    .icon-money {
      color: #f4516c;
    }

    .icon-shopping {
      color: #34bfa3;
    }

    .icon-star {
      background: #f4516c;
    }

    .card-panel-icon-wrapper {
      float: left;
      margin: 12px 0 0 10px;
      padding: 5px;
      transition: all 0.38s ease-out;
      border-radius: 6px;
    }

    .card-panel-icon {
      float: left;
      font-size: 35px;
    }

    .card-panel-description {
      float: left;
      font-weight: bold;
      margin: 20px;
      margin-left: 40px;

      .card-panel-text {
        line-height: 16px;
        color: rgba(0, 0, 0, 0.45);
        font-size: 16px;
        margin-bottom: 5px;
        // margin-left: 10px;
      }
      .card-panel-text span{
        font-size: 12px;
      }

      .card-panel-num {
         font-family: DINCondensed-Bold;
          font-size: 16px;
          color: #303987;
      }
    }
  }
}

@media (max-width:550px) {
  .card-panel-description {
    display: none;
  }

  .card-panel-icon-wrapper {
    float: none !important;
    width: 100%;
    height: 100%;
    margin: 0 !important;

    .svg-icon {
      display: block;
      margin: 14px auto !important;
      float: none !important;
    }
  }
}
</style>
