import * as THREE from 'three';
import {obj} from './share.js';
const height = 3;

// 原始3D板卡模型
export function initBanka() {
        const obj2 = {
            metalness: 0.6,  // 降低金属度，减少反光
            roughness: 0.1,  // 增加粗糙度，减少反光
            shininess: 10,  // 降低高光指数
            x: 0,
            y: 0,
            z: 0,
        };

        // 解析参数
        // const {   } = params || {};
        // const U = height / (buttonCount || 48); // 默认值为48

        const bankaBox = new THREE.BoxGeometry(1, 0.2, 1.5);
        // const textureLoader = new THREE.TextureLoader();
        const materials = new THREE.MeshPhysicalMaterial({
            color: 0x75dbf3, // 如果是连续区域则使用绿色，否则使用蓝色
            antialias: true,
            metalness: obj2.metalness,
            roughness: obj2.roughness,
            shininess: obj2.shininess,
            // opacity: 0.7,    // 透明度 0.7
            // transparent: true, // 开启透明
        })

            const group = new THREE.Group();
            const banka = new THREE.Mesh(bankaBox, materials);
            // jhj.rotation.y = -Math.PI / 2;
            return banka
}

// 新的2D板卡模型
export function init2DBanka() {
    // 创建一个平面几何体，宽高比例与图片类似 - 更宽的横向矩形
    const planeGeometry = new THREE.PlaneGeometry(2.0, 0.6); // 更宽的横向矩形

    // 创建材质 - 使用浅灰色，类似于图片中的颜色
    const material = new THREE.MeshBasicMaterial({
        color: 0xdddddd, // 浅灰色
        side: THREE.DoubleSide // 双面可见
    });

    // 创建边框材质
    const edgeMaterial = new THREE.LineBasicMaterial({
        color: 0x999999, // 灰色边框
        linewidth: 1 // 线宽
    });

    // 创建板卡平面
    const plane = new THREE.Mesh(planeGeometry, material);

    // 创建边框
    const edges = new THREE.EdgesGeometry(planeGeometry);
    const line = new THREE.LineSegments(edges, edgeMaterial);

    // 创建一个组来包含平面和边框
    const group = new THREE.Group();
    group.add(plane);
    group.add(line);

    // 添加一个向上的箭头图标
    const arrowWidth = 0.1;
    const arrowHeight = 0.2;
    const arrowGeometry = new THREE.BufferGeometry();
    const arrowPoints = [
        new THREE.Vector3(0, -arrowHeight/2, 0.001),  // 箭头底部
        new THREE.Vector3(0, arrowHeight/2, 0.001),   // 箭头顶部
        new THREE.Vector3(-arrowWidth/2, 0, 0.001),   // 箭头左翼
        new THREE.Vector3(0, arrowHeight/2, 0.001),   // 箭头顶部（重复以便绘制右翼）
        new THREE.Vector3(arrowWidth/2, 0, 0.001)     // 箭头右翼
    ];
    arrowGeometry.setFromPoints(arrowPoints);
    const arrowMaterial = new THREE.LineBasicMaterial({ color: 0x666666 });
    const arrow = new THREE.Line(arrowGeometry, arrowMaterial);
    group.add(arrow);

    // 添加一些简单的文本模拟（使用小矩形代替）
    for (let i = 0; i < 3; i++) {
        const textLineGeometry = new THREE.PlaneGeometry(0.3, 0.03);
        const textLineMaterial = new THREE.MeshBasicMaterial({ color: 0xaaaaaa });
        const textLine = new THREE.Mesh(textLineGeometry, textLineMaterial);
        textLine.position.set(0, -0.15 - i * 0.08, 0.001); // 在箭头下方排列
        group.add(textLine);
    }

    // 确保2D模型正面朝向相机
    group.rotation.x = 0; // 不需要旋转，直接面向相机

    return group;
}