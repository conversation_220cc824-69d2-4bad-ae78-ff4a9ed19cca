<template>
    <div class="app-container">
      <!-- <button @click="isMotorsVis">测试按钮</button> -->
      <div ref="threeContainer" id = "threeContainer" ></div>
    </div>
  </template>
  
  <script>
  import { defineCameraPosition , defineCard, initRenderen,updateShow3DPattern, updateControlsTarget} from './Rendering/render.js'; // 确保路径正确
  import * as THREE from 'three';
  import { generateUUID } from 'three/src/math/MathUtils.js';
  import { listRmCabinet, getRmCabinet, delRmCabinet, addRmCabinet, updateRmCabinet } from "@/api/cabinet/RmCabinet";
  import { listRmRoom, getRmRoom, delRmRoom, addRmRoom, updateRmRoom } from "@/api/room/RmRoom";
  import { initModel } from './model/allModel.js';
  import { modelVis , addInnerCube} from './Visual/modelVis.js'
  import Editor from './Editor.vue';
  export default {
    name: 'index',
    components: {
      Editor,
    },
    props: {
      show3DPattern: {
        type: <PERSON><PERSON>an,
      },
      selectedRoomId: {
        type: String,
        default: null
      },
    },
    data() {
      return {
        innerContainer: true, // 初始状态为可见
        selectedCabinet: null, // 用于存储选中的机柜信息
        index : null,
        model : null, // 存储所有模型
        motor : null,
        // result: null, //存储所有模型
        // motor2: null, // 存储机柜外框
        // result2: null,
        card: null,
        obj: null,
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 20,
          deptId: null,
          status: null,
          remarks: null,
          cabinetName: null,
          cabinetCode: null,
          roomId: null,
          roomName: null,
          totalU: null,
          manufacturer: null,
          cabinetType: null,
          length: null,
          width: null,
          height: null,
          positionX: null,
          positionY: null,
          orientation: null,
          modelId: null,
          modelName: null
        },
          // 机柜模板管理表格数据
          RmCabinetModelList: [],
          RmRoomList: [],
      };
    },
    mounted() {


    },
    
    async created() {
        try {
            // 通知父组件开始加载
            this.$emit('loading-start');

            // 根据传入的机房ID获取数据
            await this.getList(this.selectedRoomId);
            await defineCameraPosition(this.RmRoomList);

            // 初始化模型（包括墙壁、机柜、走向架等）
            let model = await initModel(this.RmCabinetModelList, this.RmRoomList, this.selectedRoomId);
            this.$emit('update-RmCabinetModelList', this.RmCabinetModelList);
            this.$emit('update-RmRoomList', this.RmRoomList);

            if (!model || !model.getObjectByName("motor")) {
                console.error('Model initialization failed');
                this.$emit('loading-end');
                return;
            }

            this.model = model;
            this.motor = model.getObjectByName("motor");

            // 初始化渲染器
            this.obj = initRenderen(this.model, this.updateSelectedCabinet);

            // 等待渲染器完全初始化
            await this.$nextTick();

            // 通知父组件加载完成
            this.$emit('loading-end');

        } catch (error) {
            console.error('3D场景初始化失败:', error);
            this.$emit('loading-end');
        }
    },
    watch: {
      innerContainer(newVal){
        if (this.motor) {
          modelVis(this.motor,newVal);
          addInnerCube(this.motor,newVal,this.RmCabinetModelList);
        } else {
          console.warn('Motor not initialized yet, skipping modelVis and addInnerCube calls');
        }
      },
      show3DPattern(newVal) {
        updateShow3DPattern(newVal);
      },
      selectedCabinet(newVal) {
        // console.log("newVal==",newVal);
        this.$emit('update-selected-cabinet', newVal); // 触发自定义事件
      },
      selectedRoomId(newVal, oldVal) {
        if (newVal !== oldVal && newVal) {
          console.log('机房切换:', newVal);
          this.refreshRoomData(newVal);
        }
      },
    },
    methods: {
        async getList(roomId = null) {
            this.loading = true;
            try {
                // 只在必要时获取机房列表
                if (!this.RmRoomList.length) {
                    const RmRoomList = await listRmRoom(this.queryParams);
                    this.RmRoomList = RmRoomList.rows;
                    this.total = RmRoomList.total;
                }

                // 根据机房ID过滤机柜数据
                const cabinetQueryParams = {
                    ...this.queryParams,
                    pageNum: 1,
                    pageSize: 1000 // 获取所有机柜
                };
                if (roomId) {
                    cabinetQueryParams.roomId = roomId;
                }

                const RmCabinetModelList = await listRmCabinet(cabinetQueryParams);
                this.RmCabinetModelList = RmCabinetModelList.rows || [];
                this.total = RmCabinetModelList.total;
            } catch (error) {
                console.error('Failed to fetch data:', error);
                throw error;
            } finally {
                this.loading = false;
            }
        },

        // 刷新机房数据
        async refreshRoomData(roomId) {
            try {
                // 通知父组件开始加载
                this.$emit('loading-start');
                console.log('开始切换机房:', roomId);

                // 重新获取机柜数据
                await this.getList(roomId);
                console.log('机房数据获取完成，机柜数量:', this.RmCabinetModelList.length);

                // 重新初始化3D模型
                await this.reinitializeModel();
                console.log('3D模型重新初始化完成');

                // 等待一个渲染周期，确保所有模型都已渲染
                await this.$nextTick();
                await new Promise(resolve => setTimeout(resolve, 100));

                // 通知父组件加载完成
                this.$emit('loading-end');
                console.log('机房切换完成');

            } catch (error) {
                console.error('刷新机房数据失败:', error);
                this.$emit('loading-end');
                throw error;
            }
        },

        // 重新初始化3D模型
        async reinitializeModel() {
            try {
                // 清除当前选中的机柜
                this.selectedCabinet = null;

                // 更高效的场景清理
                if (this.obj && this.obj.scene) {
                    // 清除所有旧模型，包括机柜、墙壁、走向架等
                    const oldModel = this.obj.scene.getObjectByName('Group');
                    if (oldModel) {
                        this.obj.scene.remove(oldModel);
                    }

                    // 也清除可能单独添加的motor组
                    const motorsGroup = this.obj.scene.getObjectByName('motor');
                    if (motorsGroup) {
                        this.obj.scene.remove(motorsGroup);
                    }
                }

                // 重新定义相机位置和旋转中心
                await defineCameraPosition(this.RmRoomList);
                console.log('机房切换 - 重新定义相机位置完成');

                // 重新初始化模型（包括机柜、墙壁、走向架等）
                console.log('开始重新初始化模型...');
                let model = await initModel(this.RmCabinetModelList, this.RmRoomList, this.selectedRoomId);
                console.log('模型初始化完成，包含的子对象:', model.children.map(child => child.name || child.type));

                this.$emit('update-RmCabinetModelList', this.RmCabinetModelList);
                this.$emit('update-RmRoomList', this.RmRoomList);

                if (!model || !model.getObjectByName("motor")) {
                    console.error('Model initialization failed - motor not found');
                    return;
                }

                this.model = model;
                this.motor = model.getObjectByName("motor");

                // 将新模型添加到现有场景中
                if (this.obj && this.obj.scene) {
                    this.obj.scene.add(this.model);
                    console.log('新模型已添加到场景');

                    // 等待多个渲染周期，确保所有异步加载的模型都已完成
                    await this.$nextTick();
                    await new Promise(resolve => setTimeout(resolve, 200));

                    // 强制更新控制中心点
                    this.updateCameraAndControls();
                } else {
                    // 如果场景不存在，重新初始化渲染器
                    console.log('重新初始化渲染器');
                    this.obj = initRenderen(this.model, this.updateSelectedCabinet);
                }

                console.log('机房切换 - 3D模型重新初始化完成');

            } catch (error) {
                console.error('重新初始化3D模型失败:', error);
                throw error;
            }
        },

        // 更新相机位置和控制中心
        updateCameraAndControls() {
            if (this.obj && this.obj.controls && this.RmRoomList && this.RmRoomList.length > 0) {
                console.log('开始更新相机和控制中心...');
                console.log('当前选中机房ID:', this.selectedRoomId);
                console.log('可用机房列表:', this.RmRoomList.map(room => ({id: room.uuid, name: room.roomName})));

                // 修复字段名：使用uuid而不是roomId
                const roomData = this.RmRoomList.find(room => room.uuid == this.selectedRoomId) || this.RmRoomList[0];
                console.log('使用的机房数据:', roomData);

                // 计算机房中心点
                const roomCenterX = roomData.width / 2;
                const roomCenterZ = roomData.length / 2;

                // 更新相机位置
                if (this.obj.camera) {
                    const newCameraY = roomData.width * 1.2;
                    this.obj.camera.position.set(-5, newCameraY, 3);
                    console.log('更新相机位置:', -5, newCameraY, 3);
                }

                // 更新控制中心点
                this.obj.controls.target.set(roomCenterX, 0, roomCenterZ);
                this.obj.controls.update();

                console.log('机房切换 - 更新旋转中心:', roomCenterX, 0, roomCenterZ);
                console.log('控制器目标位置已设置为:', this.obj.controls.target);

                // 调用render.js中的更新函数确保同步，传递当前机房ID
                updateControlsTarget(this.selectedRoomId);

                // 强制触发一次渲染更新
                if (this.obj.controls) {
                    this.obj.controls.update();
                }
            } else {
                console.warn('无法更新相机和控制中心 - 缺少必要的对象或数据');
                console.log('obj存在:', !!this.obj);
                console.log('controls存在:', !!(this.obj && this.obj.controls));
                console.log('RmRoomList存在且有数据:', !!(this.RmRoomList && this.RmRoomList.length > 0));
            }
        },
    //   handleCardClick(event) {
    //     event.stopPropagation();
    //   },
    //   async getList() {
    //     this.loading = true;
    //     listRmRoom(this.queryParams).then(response => {
    //       this.RmRoomList = response.rows;
    //       this.total = response.total;
    //       this.loading = false;
    //     });
    //     listRmCabinet(this.queryParams).then(response => {
    //       this.RmCabinetModelList = response.rows;
    //       // console.log("RmCabinetModelList",this.RmCabinetModelList);
    //       // console.log("RmRoomList",this.RmRoomList);
    //       this.total = response.total;
    //       this.loading = false;
    //       this.initCard();
          
    //     });
    //   },
    //   initCard() {
    //     const compDiv = this.$refs.cardId;
    //     this.card = compDiv;
    //     const threeContainer = this.$refs.threeContainer;
    //     const miniContainer = this.$refs.miniContainer;
    //     const result = index(compDiv, this.updateSelectedCabinet,
    //     threeContainer,this.RmCabinetModelList,
    //     this.RmRoomList,miniContainer
    //   );
    //     this.result = result;
    //     this.motors = result.getObjectByName('motor').getObjectByName('jigui');
    //     // this.miniContainer = miniContainer;
    //     console.log('this.motor2',this.motor2);
        
    //   },
      updateSelectedCabinet(cabinetData,index) {
        if(cabinetData && index){
          this.selectedCabinet = cabinetData;
          this.index = index;
          // console.log('this.selectedCabinet====',this.selectedCabinet);
          // console.log('this.index====',this.index);
        }
        
        },
      isMotorsVis() {
        this.innerContainer = !this.innerContainer;
        
      },
        
    }
  };
  </script>
  
  <style lang="scss" scoped>
  ::v-deep.app-container{
    padding:0px
  }
  /* 样式可以根据需要调整 */
  // .card {
  //   /* border: red 5px solid; */
  //   position: fixed;
  //   top: 85px;
  //   right: 0px;
  //   z-index: 1;
  //   height: 700px;
  //   width: 30%;
  //   position: absolute;
  //   display: flex;
  //   flex:row;
  //   color: rgb(253, 31, 31);
  //   background: rgb(113, 243, 178);
  //   opacity: 0; /* 初始透明度为0 */
  //   transition: opacity 1.5s ease-in-out; /* 定义过渡效果 */
  //   border:1px solid blue;
  // }
  // .miniContainer {
  //   /* border: blue 5px solid; */
  //   width: 350px;
  //   height: 400px;
  //   flex: 1;
  //   /* border:1px solid red; */
  // }
  // .card-content{
  //     /* border: red 5px solid; */
  //   flex:1;
  // }
  /* .abc {
    padding: 0px;
    margin: 0px;
    width: 100%;
  } */
  /* .lvse {
    height: 100vh;
    width: 100%;
    border: 1px solid red;
    transition: width 0.3s ease; /* 添加过渡效果 */
  /* } */
  </style>
  