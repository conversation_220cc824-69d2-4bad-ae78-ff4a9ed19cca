import request from '@/utils/request'

// 查询设备管理列表
export function listRmEquipment(query) {
  return request({
    url: '/equipment/RmEquipment/list',
    method: 'get',
    params: query
  })
}

// 查询设备管理详细
export function getRmEquipment(id) {
  return request({
    url: '/equipment/RmEquipment/' + id,
    method: 'get'
  })
}

// 新增设备管理
export function addRmEquipment(data) {
  return request({
    url: '/equipment/RmEquipment',
    method: 'post',
    data: data
  })
}

// 修改设备管理
export function updateRmEquipment(data) {
  return request({
    url: '/equipment/RmEquipment',
    method: 'put',
    data: data
  })
}

// 删除设备管理
export function delRmEquipment(id) {
  return request({
    url: '/equipment/RmEquipment/' + id,
    method: 'delete'
  })
}

// 查询设备管理列表
export function listRmEquipments(query) {
  return request({
    url: '/equipment/RmEquipment/lists',
    method: 'get',
    params: query
  })
}
