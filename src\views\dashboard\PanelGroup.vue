<template>
  <el-row :gutter="40" class="panel-group">
    <el-col :xs="12" :sm="12" :lg="12" class="card-panel-col">
      <div class="content_item">
        <c-card title="研发项目">
          <el-row :gutter="24">
            <el-col :span="6">
              <div class="title" style="height: 160px;margin:20px 10px;">
                <el-progress type="circle" :stroke-width="24" :percentage="this.panelData.YFLV"></el-progress>
              </div>
            </el-col>
            <el-col :span="18">
              <div style="height: 160px;margin:10px;">
                <el-row :gutter="24">
                  <el-col :span="12">
                    <div class="card-panel">
                      <div class="card-panel-icon-wrapper icon-people">
                        <svg-icon icon-class="example" class-name="card-panel-icon" />
                      </div>
                      <div class="card-panel-description">
                        <div class="card-panel-text">
                          研发投入(万元)
                        </div>
                        <span class="card-panel-num">{{this.panelData.LZ }} / {{this.panelData.TALZ}}</span>
                      </div>
                    </div>
                  </el-col>
                  <el-col :span="12">
                    <div class="card-panel">
                      <div class="card-panel-icon-wrapper icon-shopping">
                        <svg-icon icon-class="chart" class-name="card-panel-icon" />
                      </div>
                      <div class="card-panel-description">
                        <div class="card-panel-text">
                          项目立项（万元）
                        </div>
                        <span class="card-panel-num">{{ this.panelData.LX }}</span>
                      </div>
                    </div>
                  </el-col>
                </el-row>
                <el-row :gutter="24">
                  <el-col :span="12">
                    <div class="card-panel">
                      <div class="card-panel-icon-wrapper icon-message">
                        <svg-icon icon-class="dashboard" class-name="card-panel-icon" />
                      </div>
                      <div class="card-panel-description">
                        <div class="card-panel-text">
                          复核工时
                        </div>
                        <span class="card-panel-num">{{ this.panelData.GS }}</span>
                      </div>
                    </div>
                  </el-col>
                  <el-col :span="12">
                    <div class="card-panel">
                      <div class="card-panel-icon-wrapper icon-money">
                        <svg-icon icon-class="star" class-name="card-panel-icon" />
                      </div>
                      <div class="card-panel-description">
                        <div class="card-panel-text">
                          省级项目
                        </div>
                        <span class="card-panel-num">{{ this.panelData.SJ }} / {{this.panelData.TASJ}}</span>
                      </div>
                    </div>
                  </el-col>
                </el-row>
              </div>
            </el-col>
          </el-row>
        </c-card>
      </div>
    </el-col>
    <el-col :xs="12" :sm="12" :lg="12" class="card-panel-col">
      <div class="content_item">
        <c-card title="研发成果">
          <el-row :gutter="24">
            <el-col :span="6">
              <div class="title" style="height: 160px;margin:20px 10px;">
                <el-progress type="circle" :stroke-width="24" :percentage="this.panelData.RZLV"></el-progress>
              </div>
            </el-col>
            <el-col :span="18">
              <div style="height: 160px;margin:10px;">
                <el-row :gutter="24">
                  <el-col :span="12">
                    <div class="card-panel">
                      <div class="card-panel-icon-wrapper icon-people">
                        <svg-icon icon-class="example" class-name="card-panel-icon" />
                      </div>
                      <div class="card-panel-description">
                        <div class="card-panel-text">
                          计算机软著
                        </div>
                        <span class="card-panel-num">{{ this.panelData.RZ }} / {{this.panelData.TARZ}}</span>
                      </div>
                    </div>
                  </el-col>
                  <el-col :span="12">
                    <div class="card-panel">
                      <div class="card-panel-icon-wrapper icon-shopping">
                        <svg-icon icon-class="chart" class-name="card-panel-icon" />
                      </div>
                      <div class="card-panel-description">
                        <div class="card-panel-text">
                          发明专利
                        </div>
                        <span class="card-panel-num">{{ this.panelData.ZL }} / {{this.panelData.TAZL}}</span>
                      </div>
                    </div>
                  </el-col>
                </el-row>
                <el-row :gutter="24">
                  <el-col :span="12">
                    <div class="card-panel">
                      <div class="card-panel-icon-wrapper icon-message">
                        <svg-icon icon-class="dashboard" class-name="card-panel-icon" />
                      </div>
                      <div class="card-panel-description">
                        <div class="card-panel-text">
                          科创发文
                        </div>
                        <span class="card-panel-num">{{ this.panelData.FW }} / {{this.panelData.TAFW}}</span>
                      </div>
                    </div>
                  </el-col>
                  <el-col :span="12">
                    <div class="card-panel">
                      <div class="card-panel-icon-wrapper icon-money">
                        <svg-icon icon-class="star" class-name="card-panel-icon" />
                      </div>
                      <div class="card-panel-description">
                        <div class="card-panel-text">
                          参与研发人数
                        </div>
                        <span class="card-panel-num">{{ this.panelData.CY }}</span>
                      </div>
                    </div>
                  </el-col>
                </el-row>
              </div>
            </el-col>
          </el-row>
        </c-card>
      </div>
    </el-col>
  </el-row>
</template>

<script>
import CCard from "@/components/CCard";
export default {
  components: {
    CCard
  },
  props: {
    panelData: {
      type: Object,
      required: true
    }
  }
};
</script>

<style lang="scss" scoped>
.panel-group {
  margin-top: 0px;

  .card-panel-col {
    margin-bottom: 12px;
  }
  .content_item {
    height: 220px;
    cursor: pointer;
    font-size: 12px;
    position: relative;
    overflow: hidden;
    color: #666;
    background: #fff;
    box-shadow: 4px 4px 40px rgba(0, 0, 0, 0.05);
    border-color: rgba(0, 0, 0, 0.05);
  }
  .card-panel {
    width: 100%;
    height: 70px;
    box-shadow: 0 3px 11px 0 rgba(0, 0, 0, 0.1);
    border-radius: 5px;
    float: left;
    margin-bottom: 10px;
    text-align: center;

    &:hover {
      .card-panel-icon-wrapper {
        color: #fff;
      }

      .icon-people {
        background: #ff00ff;
      }

      .icon-message {
        background: #36a3f7;
      }

      .icon-money {
        background: #f4516c;
      }

      .icon-shopping {
        background: #34bfa3;
      }

      .icon-star {
        background: #f4516c;
      }
    }

    .icon-people {
      color: #ff00ff;
    }

    .icon-message {
      color: #36a3f7;
    }

    .icon-money {
      color: #f4516c;
    }

    .icon-shopping {
      color: #34bfa3;
    }

    .icon-star {
      background: #f4516c;
    }

    .card-panel-icon-wrapper {
      float: left;
      margin: 12px 0 0 10px;
      padding: 5px;
      transition: all 0.38s ease-out;
      border-radius: 6px;
    }

    .card-panel-icon {
      float: left;
      font-size: 35px;
    }

    .card-panel-description {
      float: left;
      font-weight: bold;
      margin: 20px;
      margin-left: 40px;

      .card-panel-text {
        line-height: 16px;
        color: rgba(0, 0, 0, 0.45);
        font-size: 16px;
        margin-bottom: 5px;
        // margin-left: 10px;
      }
      .card-panel-text span {
        font-size: 12px;
      }

      .card-panel-num {
        font-family: DINCondensed-Bold;
        font-size: 16px;
        color: #303987;
      }
    }
  }
}

@media (max-width: 550px) {
  .card-panel-description {
    display: none;
  }

  .card-panel-icon-wrapper {
    float: none !important;
    width: 100%;
    height: 100%;
    margin: 0 !important;

    .svg-icon {
      display: block;
      margin: 14px auto !important;
      float: none !important;
    }
  }
}
</style>
