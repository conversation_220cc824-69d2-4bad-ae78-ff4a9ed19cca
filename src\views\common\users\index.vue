<template>
  <div>
    <el-dialog :title="title" :visible.sync="dialogFormVisible"  width="60%" height="60vh" append-to-body :close-on-click-modal="false" @close="handleClose()" @open="open()">
      <template slot="title">
        <div class="avue-crud__dialog__header" style="border-bottom:1px solid #DCDFE6;padding-bottom:15px;">
          <span class="el-dialog__title">
            <span
              style="display:inline-block;background-color: #3478f5;width:3px;height:20px;margin-right:5px; float: left;margin-top:2px;margin-right:15px"></span>
            {{title}}
          </span>
        </div>
      </template>
    <el-row :gutter="20">
      <!--部门数据-->
      <el-col :span="8" :xs="24">

        <div class="head-container">
          <el-input
            v-model="deptName"
            placeholder="请输入部门名称"
            clearable
            size="small"
            prefix-icon="el-icon-search"
            style="margin-bottom: 20px"
          />
        </div>
        <div class="branch">
          <div class="head-container app-depets">
            <el-tree
              :data="deptOptions"
              :props="defaultProps"
              :expand-on-click-node="false"
              :filter-node-method="filterNode"
              ref="tree"
              highlight-current
              @node-click="handleNodeClick"
            />
          </div>
        </div>
      </el-col>
      <!--用户数据-->
      <!-- <el-col :span="16" :xs="24"> -->
        <!-- <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
          <el-form-item label="姓名" prop="nickName">
            <el-input
              v-model="queryParams.nickName"
              placeholder="请输入姓名"
              clearable
              style="width: 220px"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form> -->
    <!-- <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          :disabled="multiple"
          @click="submitForm"
        >选择</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row> -->
        <!-- <div class="head-container app-depets"> -->
        <!-- <el-table :data="userList" @selection-change="handleSelectionChange" stripe border height="60vh">
          <el-table-column type="selection" width="50" align="center" />
          <el-table-column prop="" label="序号" width="50" align="center" type="index" :index="indexMethod"></el-table-column>
          <el-table-column label="部门" align="center" key="deptName" prop="dept.deptName" />
          <el-table-column label="姓名" align="center" key="nickName" prop="nickName" />
        </el-table> -->

        <!-- </div> -->

        <!-- <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        /> -->
      <!-- </el-col> -->
      <el-col :span="16">
        <!-- <el-transfer
          filterable
          :filter-method="filterMethod"
          filter-placeholder="请输入姓名"
          v-model="value"
          :titles="['待选人员', '已选人员']"
          :data="data">
        </el-transfer> -->

          <el-transfer
          v-model="value"
          filterable
          :titles="['待选人员', '已选人员']"
          :data="checkuserdata">
        </el-transfer>

      </el-col>
    </el-row>
        <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 认</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getlistUser, deptTreeSelect } from "@/api/system/user";
import { getToken } from "@/utils/auth";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  name: "User",
  dicts: ["sys_normal_disable", "sys_user_sex"],
  components: { Treeselect },
  data() {
    return {
      checkuserdata: [],
      value: [],
      // filterMethod(query, item) {
      //   return item.pinyin.indexOf(query) > -1;
      // },
      // value4: [1],
      renderFunc(h, option) {
        return (
          <span>
            {option.key} - {option.label}
          </span>
        );
      },

      dialogFormVisible: false,
      // 遮罩层
      loading: true,
      type:'',
      // 选中数组
      // ids: {
      //   id:null,
      //   value:null
      //   },
      ids: {
        username: [],
        userid: []
      },
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 用户表格数据
      userList: [],
      // 弹出层标题
      title: "选择人员",
      // 部门树选项
      deptOptions: undefined,
      // 部门名称
      deptName: undefined,
      // 日期范围
      dateRange: [],
      // 岗位选项
      postOptions: [],
      // 角色选项
      roleOptions: [],
      // 表单参数
      form: {},
      defaultProps: {
        children: "children",
        label: "label"
      },
      // 查询参数
      queryParams: {
        // pageNum: 1,
        // pageSize: 10,
        deptId: '',
        nickName: ''
      }
    };
  },
  watch: {
    // 根据名称筛选部门树
    deptName(val) {
      this.$refs.tree.filter(val);
    }
  },
  methods: {
    handleClose() {
      this.cancel();
    },
    open() {
      this.getList();
      this.getDeptTree();
    },
    /** 查询用户列表 */
    getList() {
      getlistUser(this.queryParams).then(response => {
        this.userList = response.rows;
          const data = [];
          this.userList.forEach((row) => {
            data.push({
              key: row.userId,
              label: row.nickName
            });
          });
        // this.total = response.total;
        this.checkuserdata=data;
        // debugger
      });
    },
    /** 查询部门下拉树结构 */
    getDeptTree() {
      deptTreeSelect().then(response => {
        this.deptOptions = response.data;
        // console.log("this.deptOptions");
        // console.log(this.deptOptions);
      });
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    // 节点单击事件
    handleNodeClick(data) {
      this.queryParams.deptId = data.id;
      this.handleQuery();
    },

    // 取消按钮
    cancel() {
      this.dialogFormVisible = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        userId: undefined,
        deptId: undefined,
        userName: undefined,
        nickName: undefined,
        password: undefined,
        phonenumber: undefined,
        email: undefined,
        sex: undefined,
        status: "0",
        remark: undefined,
        postIds: [],
        roleIds: []
      };
      this.queryParams={
        // pageNum: 1,
        // pageSize: 10,
        deptId: '',
          nickName: ''
      }
      this.getList()
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      // this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids.userid = selection.map(item => item.userId);
      this.ids.username = selection.map(item => item.nickName);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },

    /** 提交按钮 */
    submitForm() {
      // debugger
      this.ids.userid=this.value
      this.ids.username=[]
      this.value.forEach((item) => {
        this.checkuserdata.forEach((item1) => {
          if(item == item1.key){
            this.ids.username.push(item1.label)
          }
        })
        }
      )
      // debugger
      if(this.type==1){
        // debugger
        this.$emit("buttonEvent", this.ids);
        this.dialogFormVisible = false;
      }else if(this.type==2){
        this.$emit("ccbuttonEvent", this.ids);
        this.dialogFormVisible = false;
      }

    },
    //序号
    indexMethod(index) {
      return index + 1;
    }
  }
};
</script>
<style scoped lang="scss">
// ::v-deep .treeselect-main {
//     width: 204px;
// }

::v-deep .el-dialog__body {
    padding: 10px 20px 10px 20px;
    height: 70vh;
    overflow: auto;
}

// ::v-deep .pagination-container {
//     margin-bottom: 10px;
// }

// ::v-deep .el-upload {
//     display: inline-block;
//     /* text-align: center; */
//     cursor: pointer;
//     outline: none;
// }

// Transfer Element
// .el-transfer.transfer-high{
//   .el-transfer-panel{
//     height: 60vh;
//     // &__body{
//     //   height: 600px !important;
//     //   .el-transfer-panel__list{
//     //     height: 600px - 60 !important;
//     //   }
//     // }
//   }
// }
//修改穿梭框高度
::v-deep .el-transfer{
  .el-transfer-panel{
    .el-transfer-panel__body{
      height: 60vh !important;
      .el-transfer-panel__list{
        height: 60vh !important;
      }
    }
  }
}
.branch{
  height: 60vh;
  // border: 1px solid red;
  overflow: auto;
}
</style>
