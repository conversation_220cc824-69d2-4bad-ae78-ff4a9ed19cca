<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="120px">
      <el-form-item label="设备名称" prop="equipmentName">
        <el-input v-model="queryParams.equipmentName" placeholder="请输入设备名称" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="设备型号" prop="model">
        <el-input v-model="queryParams.model" placeholder="请输入设备型号" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="生产厂家" prop="manufacturer">
        <el-input v-model="queryParams.manufacturer" placeholder="请输入生产厂家" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="设备类型" prop="equipmentType">
        <el-select v-model="queryParams.equipmentType" placeholder="请选择设备类型" clearable>
          <el-option v-for="dict in dict.type.equipment_type" :key="dict.value" :label="dict.label"
            :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="设备品牌" prop="brand">
        <el-input v-model="queryParams.brand" placeholder="请输入设备品牌" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <!-- <el-form-item label="设备占用U的数量" prop="occupiedU">
        <el-input
          v-model="queryParams.occupiedU"
          placeholder="请输入设备占用U的数量"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="长度" prop="length">
        <el-input
          v-model="queryParams.length"
          placeholder="请输入长度"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="宽度" prop="width">
        <el-input
          v-model="queryParams.width"
          placeholder="请输入宽度"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="高度" prop="height">
        <el-input
          v-model="queryParams.height"
          placeholder="请输入高度"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option v-for="dict in dict.type.equipment_status" :key="dict.value" :label="dict.label"
            :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['model:RmEquipmentModel:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
          v-hasPermi="['model:RmEquipmentModel:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDeletes"
          v-hasPermi="['model:RmEquipmentModel:removes']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
          v-hasPermi="['model:RmEquipmentModel:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="RmEquipmentModelList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column prop="" label="序号" width="80" align="center" type="index" :index="indexMethod" />
      <el-table-column label="设备名称" align="center" prop="equipmentName" />
      <el-table-column label="设备型号" align="center" prop="model" />
      <el-table-column label="生产厂家" align="center" prop="manufacturer" />
      <el-table-column label="设备类型" align="center" prop="equipmentType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.equipment_type" :value="scope.row.equipmentType" />
        </template>
      </el-table-column>
      <el-table-column label="设备品牌" align="center" prop="brand" />
      <el-table-column label="设备占用U的数量" align="center" prop="occupiedU" />
      <el-table-column label="长度(mm)" align="center" prop="length" />
      <el-table-column label="宽度(mm)" align="center" prop="width" />
      <el-table-column label="高度(mm)" align="center" prop="height" />
      <!-- <el-table-column label="备注说明" align="center" prop="remarks" /> -->
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.equipment_status" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['model:RmEquipmentModel:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['model:RmEquipmentModel:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改设备模板管理对话框 -->
    <el-dialog :title="title" :fullscreen="dialogFull" :visible.sync="open" width="80%" append-to-body
      :close-on-click-modal="false" @close="handleClose()">
      <template slot="title">
        <div class="avue-crud__dialog__header" style="border-bottom:1px solid #DCDFE6;padding-bottom:15px;">
          <span class="el-dialog__title">
            <span
              style="display:inline-block;background-color: #3478f5;width:3px;height:20px;margin-right:5px; float: left;margin-top:2px;margin-right:15px"></span>
            {{ title }}
          </span>
          <div class="avue-crud__dialog__menu" @click="dialogFull ? dialogFull = false : dialogFull = true">
            <i class="el-icon-full-screen"></i>
          </div>
        </div>
      </template>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="8">
            <el-form-item label="设备名称" prop="equipmentName">
              <el-input v-model="form.equipmentName" placeholder="请输入设备名称" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="设备型号" prop="model">
              <el-input v-model="form.model" placeholder="请输入设备型号" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="设备类型" prop="equipmentType">
              <el-select v-model="form.equipmentType" placeholder="请选择设备类型">
                <el-option v-for="dict in dict.type.equipment_type" :key="dict.value" :label="dict.label"
                  :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="设备品牌" prop="brand">
              <el-input v-model="form.brand" placeholder="请输入设备品牌" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="生产厂家" prop="manufacturer">
              <el-input v-model="form.manufacturer" placeholder="请输入生产厂家" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="占用U数量" prop="occupiedU">
              <el-input v-model="form.occupiedU" placeholder="请输入设备占用U的数量" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="长度(mm)" prop="length">
              <el-input v-model="form.length" placeholder="请输入长度" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="宽度(mm)" prop="width">
              <el-input v-model="form.width" placeholder="请输入宽度" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="高度(mm)" prop="height">
              <el-input v-model="form.height" placeholder="请输入高度" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注说明" prop="remarks">
              <el-input v-model="form.remarks" type="textarea" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="状态">
              <el-radio-group v-model="form.status">
                <el-radio v-for="dict in dict.type.equipment_status" :key="dict.value"
                  :label="dict.value">{{ dict.label }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-divider content-position="center">设备槽位</el-divider>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" icon="el-icon-plus" size="mini"
              @click="handleAddRmEquipmentSlotModel">添加</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" icon="el-icon-delete" size="mini"
              @click="handleDeleteRmEquipmentSlotModel">删除</el-button>
          </el-col>
        </el-row>
        <el-table :data="rmEquipmentSlotModelList" :row-class-name="rowRmEquipmentSlotModelIndex"
          @selection-change="handleRmEquipmentSlotModelSelectionChange" ref="rmEquipmentSlotModel" stripe border>
          <el-table-column type="selection" width="50" align="center" />
          <el-table-column label="序号" align="center" prop="index" width="80" />
          <el-table-column label="槽位编号" prop="slotNumber" align="center">
            <template slot-scope="scope">
              <el-input v-model="scope.row.slotNumber" placeholder="请输入槽位编号" />
            </template>
          </el-table-column>
          <el-table-column label="长度(mm)" prop="length" align="center">
            <template slot-scope="scope">
              <el-input v-model="scope.row.length" placeholder="请输入长度" />
            </template>
          </el-table-column>
          <el-table-column label="宽度(mm)" prop="width" align="center">
            <template slot-scope="scope">
              <el-input v-model="scope.row.width" placeholder="请输入宽度" />
            </template>
          </el-table-column>
          <el-table-column label="高度(mm)" prop="height" align="center">
            <template slot-scope="scope">
              <el-input v-model="scope.row.height" placeholder="请输入高度" />
            </template>
          </el-table-column>
          <el-table-column label="位置X(mm)" prop="positionX"  align="center">
            <template slot-scope="scope">
              <el-input v-model="scope.row.positionX" placeholder="请输入位置X" />
            </template>
          </el-table-column>
          <el-table-column label="位置Y(mm)" prop="positionY"  align="center">
            <template slot-scope="scope">
              <el-input v-model="scope.row.positionY" placeholder="请输入位置Y" />
            </template>
          </el-table-column>
          <el-table-column label="槽位状态" prop="status" align="center">
            <template slot-scope="scope">
              <el-select v-model="scope.row.status" placeholder="请选择槽位状态">
                <el-option v-for="dict in dict.type.sys_normal_disable" :key="dict.value" :label="dict.label"
                  :value="dict.value"></el-option>
              </el-select>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listRmEquipmentModel, getRmEquipmentModel, delRmEquipmentModel, addRmEquipmentModel, updateRmEquipmentModel } from "@/api/model/RmEquipmentModel";

export default {
  name: "RmEquipmentModel",
  dicts: ['equipment_status', 'equipment_type', 'sys_normal_disable'],
  data() {
    return {
      //是否弹窗全屏
      dialogFull: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 子表选中数据
      checkedRmEquipmentSlotModel: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 设备模板管理表格数据
      RmEquipmentModelList: [],
      // 设备槽位模板管理表格数据
      rmEquipmentSlotModelList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        remarks: null,
        equipmentName: null,
        model: null,
        manufacturer: null,
        equipmentType: null,
        brand: null,
        occupiedU: null,
        length: null,
        width: null,
        height: null,
        status: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        equipmentName: [
          { required: true, message: "设备名称不能为空", trigger: "blur" }
        ],
        length: [
          { required: true, message: "长度不能为空", trigger: "blur" }
        ],
        width: [
          { required: true, message: "宽度不能为空", trigger: "blur" }
        ],
        height: [
          { required: true, message: "高度不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    indexMethod(index) {
      return (
        index + (this.queryParams.pageNum - 1) * this.queryParams.pageSize + 1
      );
    },
    handleChange(val) {
      // console.log(val);
    },
    handleClick(tab, event) {
      // console.log(tab, event);
    },
    handleClose() {
      this.cancel();
    },
    /** 查询设备模板管理列表 */
    getList() {
      this.loading = true;
      listRmEquipmentModel(this.queryParams).then(response => {
        this.RmEquipmentModelList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.dialogFull = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        uuid: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        userId: null,
        deptId: null,
        filetime: null,
        remarks: null,
        delFlag: null,
        equipmentName: null,
        model: null,
        manufacturer: null,
        equipmentType: null,
        brand: null,
        occupiedU: null,
        length: null,
        width: null,
        height: null,
        status: "1"
      };
      this.rmEquipmentSlotModelList = [];
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.uuid)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.uuid || this.ids
      getRmEquipmentModel(id).then(response => {
        this.form = response.data;
        this.rmEquipmentSlotModelList = response.data.rmEquipmentSlotModelList;
        this.open = true;
        this.title = "修改";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.form.rmEquipmentSlotModelList = this.rmEquipmentSlotModelList;
          if (this.form.uuid != null) {
            updateRmEquipmentModel(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addRmEquipmentModel(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDeletes(row) {
      const ids = row.uuid || this.ids;
      this.$modal.confirm('是否确认删除选中的数据项？').then(function () {
        return delRmEquipmentModel(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.uuid || this.ids;
      this.$modal.confirm('是否确认删除数据？').then(function () {
        return delRmEquipmentModel(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 设备槽位模板管理序号 */
    rowRmEquipmentSlotModelIndex({ row, rowIndex }) {
      row.index = rowIndex + 1;
    },
    /** 设备槽位模板管理添加按钮操作 */
    handleAddRmEquipmentSlotModel() {
      let obj = {};
      obj.slotNumber = "";
      obj.length = "";
      obj.width = "";
      obj.height = "";
      obj.remarks = "";
      obj.status = "0";
      obj.positionX = "";
      obj.positionY = "";
      this.rmEquipmentSlotModelList.push(obj);
    },
    /** 设备槽位模板管理删除按钮操作 */
    handleDeleteRmEquipmentSlotModel() {
      if (this.checkedRmEquipmentSlotModel.length == 0) {
        this.$modal.msgError("请先选择要删除的设备槽位模板管理数据");
      } else {
        const rmEquipmentSlotModelList = this.rmEquipmentSlotModelList;
        const checkedRmEquipmentSlotModel = this.checkedRmEquipmentSlotModel;
        this.rmEquipmentSlotModelList = rmEquipmentSlotModelList.filter(function (item) {
          return checkedRmEquipmentSlotModel.indexOf(item.index) == -1
        });
      }
    },
    /** 复选框选中数据 */
    handleRmEquipmentSlotModelSelectionChange(selection) {
      this.checkedRmEquipmentSlotModel = selection.map(item => item.index)
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('model/RmEquipmentModel/export', {
        ...this.queryParams
      }, `导出明细_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
<style lang="scss" scoped></style>
