import * as THREE from 'three';
// import plane from './cizhuan.js';
import { FBXLoader } from 'three/examples/jsm/loaders/FBXLoader.js';
import { GUI } from 'three/examples/jsm/libs/lil-gui.module.min.js';


console.log("three",THREE);
// E:\WorkSpace_E\zhihuijifang\htzc-room-vue\src\assets\pic
const floorGeometry = new THREE.PlaneGeometry( 700, 700 );
const floortexture = new THREE.TextureLoader().load( '@/assets/pic/cizhuan.jpg' );
const floorMaterial = new THREE.MeshBasicMaterial( { 
    map: floortexture,
    side: THREE.DoubleSide,
    color : 0xb0cfcb,
 } );
 const floor = new THREE.Mesh( floorGeometry, floorMaterial );
 floor.translateX(250);
 floor.translateZ(250);
 floortexture.wrapS = THREE.RepeatWrapping;
 floortexture.wrapT = THREE.RepeatWrapping;
 floortexture.repeat.set( 100,100);
 floor.rotateX(Math.PI / 2);
 floor.position.y = -1;
 const gui = new GUI();
 const floorMaterialFolder = gui.addFolder('floorMaterial');
 floorMaterialFolder.addColor(floorMaterial,'color').name("地板颜色").onChange(function(value){
    floor.material.color.set(value);
});
 export default floor;