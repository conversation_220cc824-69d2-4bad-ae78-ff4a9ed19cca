<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="120px">
      <el-form-item label="板卡名称" prop="boardName">
        <el-input v-model="queryParams.boardName" placeholder="请输入板卡名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="板卡号" prop="boardNumber">
        <el-input v-model="queryParams.boardNumber" placeholder="请输入板卡号" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="板卡品牌" prop="brand">
        <el-input v-model="queryParams.brand" placeholder="请输入板卡品牌" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="板卡型号" prop="model">
        <el-input v-model="queryParams.model" placeholder="请输入板卡型号" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <!-- <el-form-item label="所在槽位ID" prop="slotId">
        <el-input
          v-model="queryParams.slotId"
          placeholder="请输入所在槽位ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="所属槽位号" prop="slotNumber">
        <el-input
          v-model="queryParams.slotNumber"
          placeholder="请输入所属槽位号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="长度" prop="length">
        <el-input
          v-model="queryParams.length"
          placeholder="请输入长度"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="宽度" prop="width">
        <el-input
          v-model="queryParams.width"
          placeholder="请输入宽度"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="高度" prop="height">
        <el-input
          v-model="queryParams.height"
          placeholder="请输入高度"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="板卡状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择板卡状态" clearable>
          <el-option v-for="dict in dict.type.sys_normal_disable" :key="dict.value" :label="dict.label"
            :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['model:RmEquipmentBoardModel:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
          v-hasPermi="['model:RmEquipmentBoardModel:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDeletes"
          v-hasPermi="['model:RmEquipmentBoardModel:removes']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
          v-hasPermi="['model:RmEquipmentBoardModel:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>
    <el-table v-loading="loading" :data="RmEquipmentBoardModelList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column prop="" label="序号" width="80" align="center" type="index" :index="indexMethod" />
      <el-table-column label="板卡名称" align="center" prop="boardName" />
      <el-table-column label="板卡类型" align="center" prop="boardType" />
      <!-- <el-table-column label="板卡号" align="center" prop="boardNumber" /> -->
      <el-table-column label="板卡品牌" align="center" prop="brand" />
      <el-table-column label="板卡型号" align="center" prop="model" />
      <!-- <el-table-column label="所在槽位ID" align="center" prop="slotId" /> -->
      <!-- <el-table-column label="所属槽位号" align="center" prop="slotNumber" /> -->
      <el-table-column label="长度(mm)" align="center" prop="length" />
      <el-table-column label="宽度(mm)" align="center" prop="width" />
      <el-table-column label="高度(mm)" align="center" prop="height" />
      <el-table-column label="板卡状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['model:RmEquipmentBoardModel:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['model:RmEquipmentBoardModel:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改设备板卡模板管理对话框 -->
    <el-dialog :title="title" :fullscreen="dialogFull" :visible.sync="open" width="70%" append-to-body
      :close-on-click-modal="false" @close="handleClose()">
      <template slot="title">
        <div class="avue-crud__dialog__header" style="border-bottom:1px solid #DCDFE6;padding-bottom:15px;">
          <span class="el-dialog__title">
            <span
              style="display:inline-block;background-color: #3478f5;width:3px;height:20px;margin-right:5px; float: left;margin-top:2px;margin-right:15px"></span>
            {{ title }}
          </span>
          <div class="avue-crud__dialog__menu" @click="dialogFull ? dialogFull = false : dialogFull = true">
            <i class="el-icon-full-screen"></i>
          </div>
        </div>
      </template>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="8">
            <el-form-item label="板卡名称" prop="boardName">
              <el-input v-model="form.boardName" placeholder="请输入板卡名称" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="板卡型号" prop="model">
              <el-input v-model="form.model" placeholder="请输入板卡型号" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="板卡类型" prop="boardType">
              <el-select v-model="form.boardType" placeholder="请选择板卡类型">
                <el-option v-for="dict in dict.type.board_type" :key="dict.value" :label="dict.label"
                  :value="dict.value"></el-option>
              </el-select>
            </el-form-item>

          </el-col>
        </el-row>

        <!-- <el-row>
          <el-col :span="8">
            <el-form-item label="板卡号" prop="boardNumber">
              <el-input v-model="form.boardNumber" placeholder="请输入板卡号" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="所在槽位ID" prop="slotId">
              <el-input v-model="form.slotId" placeholder="请输入所在槽位ID" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="所属槽位号" prop="slotNumber">
              <el-input v-model="form.slotNumber" placeholder="请输入所属槽位号" />
            </el-form-item>
          </el-col>
        </el-row> -->
        <el-row>
          <el-col :span="8">
            <el-form-item label="长度(mm)" prop="length">
              <el-input v-model="form.length" placeholder="请输入长度" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="宽度(mm)" prop="width">
              <el-input v-model="form.width" placeholder="请输入宽度" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="高度(mm)" prop="height">
              <el-input v-model="form.height" placeholder="请输入高度" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="板卡品牌" prop="brand">
              <el-input v-model="form.brand" placeholder="请输入板卡品牌" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="板卡状态">
              <el-radio-group v-model="form.status">
                <el-radio v-for="dict in dict.type.sys_normal_disable" :key="dict.value" :label="dict.value">{{
                  dict.label
                  }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注说明" prop="remarks">
              <el-input v-model="form.remarks" type="textarea" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-divider content-position="center">设备端口信息</el-divider>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" icon="el-icon-plus" size="mini"
              @click="handleAddRmEquipmentPortModel">添加</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" icon="el-icon-delete" size="mini"
              @click="handleDeleteRmEquipmentPortModel">删除</el-button>
          </el-col>
        </el-row>
        <el-table :data="rmEquipmentPortModelList" :row-class-name="rowRmEquipmentPortModelIndex"
          @selection-change="handleRmEquipmentPortModelSelectionChange" ref="rmEquipmentPortModel" stripe border>
          <el-table-column type="selection" width="50" align="center" />
          <el-table-column label="序号" align="center" prop="index" width="50" />
          <!-- <el-table-column label="板卡编号" prop="boardName">
            <template slot-scope="scope">
              <el-input v-model="scope.row.boardName" placeholder="请输入板卡编号" />
            </template>
          </el-table-column> -->
          <el-table-column label="端口编号" prop="portNumber" align="center">
            <template slot-scope="scope">
              <el-input v-model="scope.row.portNumber" placeholder="请输入端口编号" />
            </template>
          </el-table-column>
          <el-table-column label="端口类型" prop="portType" align="center">
            <template slot-scope="scope">
              <el-select v-model="scope.row.portType" placeholder="请选择端口类型">
                <el-option v-for="dict in dict.type.port_type" :key="dict.value" :label="dict.label"
                  :value="dict.value"></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="长(mm)" prop="length" align="center">
            <template slot-scope="scope">
              <el-input v-model="scope.row.length" placeholder="请输入长" />
            </template>
          </el-table-column>
          <el-table-column label="宽(mm)" prop="width" align="center">
            <template slot-scope="scope">
              <el-input v-model="scope.row.width" placeholder="请输入宽" />
            </template>
          </el-table-column>
          <el-table-column label="位置X(mm)" prop="positionX" align="center">
            <template slot-scope="scope">
              <el-input v-model="scope.row.positionX" placeholder="请输入位置X" />
            </template>
          </el-table-column>
          <el-table-column label="位置Y(mm)" prop="positionY" align="center">
            <template slot-scope="scope">
              <el-input v-model="scope.row.positionY" placeholder="请输入位置Y" />
            </template>
          </el-table-column>
          <el-table-column label="端口描述" prop="description" align="center">
            <template slot-scope="scope">
              <el-input v-model="scope.row.description" placeholder="请输入端口描述" />
            </template>
          </el-table-column>
          <el-table-column label="端口状态" prop="status" align="center">
            <template slot-scope="scope">
              <el-select v-model="scope.row.status" placeholder="请选择端口状态">
                <el-option v-for="dict in dict.type.sys_normal_disable" :key="dict.value" :label="dict.label"
                  :value="dict.value"></el-option>
              </el-select>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listRmEquipmentBoardModel, getRmEquipmentBoardModel, delRmEquipmentBoardModel, addRmEquipmentBoardModel, updateRmEquipmentBoardModel } from "@/api/model/RmEquipmentBoardModel";

export default {
  name: "RmEquipmentBoardModel",
  dicts: ['board_type', 'port_type', 'sys_normal_disable'],
  data() {
    return {
      //是否弹窗全屏
      dialogFull: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 子表选中数据
      checkedRmEquipmentPortModel: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 设备板卡模板管理表格数据
      RmEquipmentBoardModelList: [],
      // 设备端口模板管理表格数据
      rmEquipmentPortModelList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        remarks: null,
        boardName: null,
        boardType: null,
        boardNumber: null,
        brand: null,
        model: null,
        slotId: null,
        slotNumber: null,
        length: null,
        width: null,
        height: null,
        status: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    indexMethod(index) {
      return (
        index + (this.queryParams.pageNum - 1) * this.queryParams.pageSize + 1
      );
    },
    handleChange(val) {
      // console.log(val);
    },
    handleClick(tab, event) {
      // console.log(tab, event);
    },
    handleClose() {
      this.cancel();
    },
    /** 查询设备板卡模板管理列表 */
    getList() {
      this.loading = true;
      listRmEquipmentBoardModel(this.queryParams).then(response => {
        this.RmEquipmentBoardModelList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.dialogFull = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        uuid: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        userId: null,
        deptId: null,
        filetime: null,
        remarks: null,
        delFlag: null,
        boardName: null,
        boardType: null,
        boardNumber: null,
        brand: null,
        model: null,
        slotId: null,
        slotNumber: null,
        length: null,
        width: null,
        height: null,
        status: "0"
      };
      this.rmEquipmentPortModelList = [];
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.uuid)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.uuid || this.ids
      getRmEquipmentBoardModel(id).then(response => {
        this.form = response.data;
        this.rmEquipmentPortModelList = response.data.rmEquipmentPortModelList;
        this.open = true;
        this.title = "修改";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.form.rmEquipmentPortModelList = this.rmEquipmentPortModelList;
          if (this.form.uuid != null) {
            updateRmEquipmentBoardModel(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addRmEquipmentBoardModel(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDeletes(row) {
      const ids = row.uuid || this.ids;
      this.$modal.confirm('是否确认删除选中的数据项？').then(function () {
        return delRmEquipmentBoardModel(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.uuid || this.ids;
      this.$modal.confirm('是否确认删除数据？').then(function () {
        return delRmEquipmentBoardModel(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 设备端口模板管理序号 */
    rowRmEquipmentPortModelIndex({ row, rowIndex }) {
      row.index = rowIndex + 1;
    },
    /** 设备端口模板管理添加按钮操作 */
    handleAddRmEquipmentPortModel() {
      let obj = {};
      obj.boardName = "";
      obj.portNumber = "";
      obj.portType = "";
      obj.description = "";
      obj.status = "";
      obj.length = "";
      obj.width = "";
      obj.positionX = "";
      obj.positionY = "";
      this.rmEquipmentPortModelList.push(obj);
    },
    /** 设备端口模板管理删除按钮操作 */
    handleDeleteRmEquipmentPortModel() {
      if (this.checkedRmEquipmentPortModel.length == 0) {
        this.$modal.msgError("请先选择要删除的设备端口模板管理数据");
      } else {
        const rmEquipmentPortModelList = this.rmEquipmentPortModelList;
        const checkedRmEquipmentPortModel = this.checkedRmEquipmentPortModel;
        this.rmEquipmentPortModelList = rmEquipmentPortModelList.filter(function (item) {
          return checkedRmEquipmentPortModel.indexOf(item.index) == -1
        });
      }
    },
    /** 复选框选中数据 */
    handleRmEquipmentPortModelSelectionChange(selection) {
      this.checkedRmEquipmentPortModel = selection.map(item => item.index)
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('model/RmEquipmentBoardModel/export', {
        ...this.queryParams
      }, `导出明细_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
<style lang="scss" scoped></style>
