<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>子页面</title>
</head>
<style>
    .bu{
        height: 700px;
        width: 400px;
        position: absolute;
        display: inline-block;
        color: white;
        background: rgb(65, 92, 244);
        opacity: 0; /* 初始透明度为0 */
        transition: opacity 1.5s ease-in-out; /* 定义过渡效果 */
    }
</style>
<body>
    <div class="card" id="cardId">模型标签<button id = 'anniu'>按钮</button></div>

</body>
</html>