import * as THREE from 'three';
import {GLTFLoader }from 'three/examples/jsm/loaders/GLTFLoader.js';
console.log(GLTFLoader);

const group = new THREE.Group();
const load = new GLTFLoader();
load.load('./pic/工厂.gltf',function (gltf){
    group.add(gltf.scene);
    gltf.scene.traverse(function(child){
        if(child.isMesh){
            child.material = new THREE.MeshPhysicalMaterial({
                metalness: 0.8,
                roughness: 0.9,
            })
        }
        document.getElementById("container").style.display = 'none';
    })
},function (xhr){
    const percentDiv = document.getElementById("per");// 获取进度条元素
    const percent = xhr.loaded / xhr.total;
    percentDiv.style.width = percent * 400 + "px";
    percentDiv.style.textIndent = percent * 400 + 5 + "px";
    percentDiv.innerHTML = Math.floor(percent*100)+'%';
    console.log("percent",percent);
    
});

export default group;
