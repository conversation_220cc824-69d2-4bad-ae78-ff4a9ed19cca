<template>
  <div class="port-tooltip" v-if="visible" :style="tooltipStyle">
    <div class="tooltip-content">
      <h4 class="tooltip-title">端口信息</h4>
      <div class="tooltip-item">
        <span class="label">端口编号：</span>
        <span class="value">{{ portData.portNumber || '未知' }}</span>
      </div>
      <div class="tooltip-item">
        <span class="label">端口类型：</span>
        <span class="value">{{ portData.portType || '未知' }}</span>
      </div>
      <div class="tooltip-item">
        <span class="label">端口状态：</span>
        <span class="value" :class="statusClass">{{ statusText }}</span>
      </div>
      <div class="tooltip-item" v-if="portData.positionX !== undefined">
        <span class="label">位置X：</span>
        <span class="value">{{ portData.positionX }}</span>
      </div>
      <div class="tooltip-item" v-if="portData.positionY !== undefined">
        <span class="label">位置Y：</span>
        <span class="value">{{ portData.positionY }}</span>
      </div>
      <div class="tooltip-item" v-if="portData.remarks">
        <span class="label">备注：</span>
        <span class="value">{{ portData.remarks }}</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Port',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    portData: {
      type: Object,
      default: () => ({})
    },
    position: {
      type: Object,
      default: () => ({ x: 0, y: 0 })
    }
  },
  computed: {
    tooltipStyle() {
      return {
        left: (this.position.x + 15) + 'px',
        top: (this.position.y - 10) + 'px'
      };
    },
    statusText() {
      if (this.portData.status === undefined || this.portData.status === null) {
        return '未知';
      }
      return this.portData.status === 0 || this.portData.status === '0' ? '正常' : '异常';
    },
    statusClass() {
      if (this.portData.status === undefined || this.portData.status === null) {
        return 'status-unknown';
      }
      return this.portData.status === 0 || this.portData.status === '0' ? 'status-normal' : 'status-error';
    }
  }
};
</script>

<style scoped>
.port-tooltip {
  position: fixed;
  background-color: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 12px;
  border-radius: 6px;
  font-size: 12px;
  z-index: 9999;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  border: 1px solid #444;
  min-width: 200px;
  max-width: 300px;
}

.tooltip-content {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.tooltip-title {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: bold;
  color: #4CAF50;
  border-bottom: 1px solid #444;
  padding-bottom: 4px;
}

.tooltip-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 10px;
}

.label {
  color: #ccc;
  font-weight: 500;
  min-width: 70px;
}

.value {
  color: white;
  font-weight: normal;
  text-align: right;
  flex: 1;
}

.status-normal {
  color: #4CAF50;
  font-weight: bold;
}

.status-error {
  color: #f44336;
  font-weight: bold;
}

.status-unknown {
  color: #ff9800;
  font-weight: bold;
}
</style>