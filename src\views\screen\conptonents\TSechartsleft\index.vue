<template>
  <div class="content">
    <div class="left">
      <div class="box" style="color:#3CE4C7;">
        <span>当前数量：</span>
        <span>101</span>
      </div>
      <div class="box" style="color:#3CE4C7;">
        <span>考核指标：</span>
        <span>10</span>
      </div>
      <div class="box" style="color:#3CE4C7;">
        <span>月度累计：</span>
        <span>10</span>
      </div>
      <div class="box" style="color:#3CE4C7;">
        <span>年度累计：</span>
        <span>10</span>
      </div>
    </div>
    <div class="right">
      <dv-decoration-9 style="width:150px;height:150px;">66%</dv-decoration-9>
    </div>
  </div>         
</template>
<script>
import * as echarts from "echarts";
export default {
  name: 'TSecharts',
  data(){
    return{
 
    }
  },
  created () {
  
  },
  methods: {
   
  }
}
</script>
<style lang="scss" scoped>
  .content{
    padding:20px;
    // padding-top:30px;
    text-align: center;
    color: #fff;
    align-items: center;
    display: flex;
    .left{
      width: 50%;
      left:50%;
      text-align: left;
      .box{
        padding-left:20%;
        height: 30px;
      }
    }
    .right{
      width: 50%;
      padding-right:10%;
      display: flex;
      align-items: center;
      text-align: center;
      font-size: 30px;
    }
  }
  </style>