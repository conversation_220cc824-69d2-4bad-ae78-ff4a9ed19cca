<template>
  <div class="add-equipment-container" @click.stop>
    <el-dialog :title="title" :visible.sync="visible" width="80%" append-to-body :close-on-click-modal="false" @close="handleClose" class="tech-dialog" @click.native.stop>
      <template slot="title">
        <div class="tech-header" @click.stop>
          <span class="tech-title" @click.stop>
            <i class="el-icon-cpu tech-icon"></i>
            {{ title }}
          </span>
        </div>
      </template>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px" class="tech-form" @click.native.stop>
        <div class="tech-form-content" @click.stop>
          <el-row :gutter="20" @click.native.stop>
            <el-col :span="8" @click.native.stop>
              <el-form-item label="设备模版" prop="modelId" @click.native.stop>
                <el-select v-model="form.modelId" placeholder="请选择设备模版" class="tech-select" @change="handleModelChange" @click.native.stop>
                  <el-option v-for="item in modelOptions" :key="item.uuid" :label="item.equipmentName" :value="item.uuid" @click.stop></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8" @click.native.stop>
              <el-form-item label="所属机房" prop="roomName" @click.native.stop>
                <el-select v-model="form.roomName" placeholder="请选择所属机房" @change="selectRoom" class="tech-select" @click.native.stop>
                  <el-option v-for="item in roomOptions" :key="item.uuid" :label="item.roomName" :value="item.roomName" @click.stop></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8" @click.native.stop>
              <el-form-item label="所属机柜" prop="cabinetName" @click.native.stop>
                <el-input v-model="form.cabinetName" placeholder="自动获取机柜信息" readonly class="tech-input" @click.native.stop />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20" @click.native.stop>
            <el-col :span="8" @click.native.stop>
              <el-form-item label="设备名称" prop="equipmentName" required @click.native.stop>
                <el-input v-model="form.equipmentName" placeholder="请输入设备名称" class="tech-input" @click.native.stop />
              </el-form-item>
            </el-col>
            <el-col :span="8" @click.native.stop>
              <el-form-item label="设备编码" prop="equipmentCode" @click.native.stop>
                <el-input v-model="form.equipmentCode" placeholder="请输入设备编码" class="tech-input" @click.native.stop />
              </el-form-item>
            </el-col>
            <el-col :span="8" @click.native.stop>
              <el-form-item label="设备IP地址" prop="ipAddress" @click.native.stop>
                <el-input v-model="form.ipAddress" placeholder="请输入设备IP地址" class="tech-input" @click.native.stop />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20" @click.native.stop>
            <el-col :span="8" @click.native.stop>
              <el-form-item label="设备型号" prop="model" @click.native.stop>
                <el-input v-model="form.model" placeholder="请输入设备型号" class="tech-input" @click.native.stop />
              </el-form-item>
            </el-col>
            <el-col :span="8" @click.native.stop>
              <el-form-item label="设备品牌" prop="brand" @click.native.stop>
                <el-input v-model="form.brand" placeholder="请输入设备品牌" class="tech-input" @click.native.stop />
              </el-form-item>
            </el-col>
            <el-col :span="8" @click.native.stop>
              <el-form-item label="设备状态" prop="status" @click.native.stop>
                <el-radio-group v-model="form.status" class="tech-radio" @click.native.stop>
                  <el-radio label="0" @click.stop>正常</el-radio>
                  <el-radio label="1" @click.stop>停用</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20" @click.native.stop>
            <el-col :span="8" @click.native.stop>
              <el-form-item label="长度(mm)" prop="length" @click.native.stop>
                <el-input v-model="form.length" placeholder="请输入长度" class="tech-input" @click.native.stop />
              </el-form-item>
            </el-col>
            <el-col :span="8" @click.native.stop>
              <el-form-item label="宽度(mm)" prop="width" @click.native.stop>
                <el-input v-model="form.width" placeholder="请输入宽度" class="tech-input" @click.native.stop />
              </el-form-item>
            </el-col>
            <el-col :span="8" @click.native.stop>
              <el-form-item label="高度(mm)" prop="height" @click.native.stop>
                <el-input v-model="form.height" placeholder="请输入高度" class="tech-input" @click.native.stop />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20" @click.native.stop>
            <el-col :span="8" @click.native.stop>
              <el-form-item label="起始U位置" prop="startU" @click.native.stop>
                <el-input v-model="form.startU" placeholder="请输入起始U位置" class="tech-input" @click.native.stop />
              </el-form-item>
            </el-col>
            <el-col :span="8" @click.native.stop>
              <el-form-item label="占用U数量" prop="occupiedU" @click.native.stop>
                <el-input v-model="form.occupiedU" placeholder="请输入占用U数量" class="tech-input" @click.native.stop />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row @click.native.stop>
            <el-col :span="24" @click.native.stop>
              <el-form-item label="备注说明" prop="remarks" @click.native.stop>
                <el-input v-model="form.remarks" type="textarea" placeholder="请输入内容" class="tech-textarea" @click.native.stop />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <div class="tech-divider" @click.stop>
          <span class="tech-divider-text" @click.stop>设备槽位管理信息</span>
        </div>

        <div class="tech-table-container" @click.stop>
          <el-table :data="rmEquipmentSlotList" :row-class-name="rowRmEquipmentSlotIndex" @selection-change="handleRmEquipmentSlotSelectionChange" ref="rmEquipmentSlot" stripe border class="tech-table" @click.native.stop>
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column prop="" label="序号" width="80" align="center">
              <template slot-scope="scope">
                <span @click.stop>{{ scope.row.index }}</span>
              </template>
            </el-table-column>
            <el-table-column label="槽位编号" align="center" prop="slotNumber">
              <template slot-scope="scope">
                <el-input v-model="scope.row.slotNumber" placeholder="请输入槽位编号" class="tech-input" @click.native.stop />
              </template>
            </el-table-column>
            <el-table-column label="长度" align="center" prop="positionX">
              <template slot-scope="scope">
                <el-input v-model="scope.row.positionX" placeholder="请输入长度" class="tech-input" @click.native.stop />
              </template>
            </el-table-column>
            <el-table-column label="宽度" align="center" prop="length">
              <template slot-scope="scope">
                <el-input v-model="scope.row.length" placeholder="请输入宽度" class="tech-input" @click.native.stop />
              </template>
            </el-table-column>
            <el-table-column label="高度" align="center" prop="width">
              <template slot-scope="scope">
                <el-input v-model="scope.row.width" placeholder="请输入高度" class="tech-input" @click.native.stop />
              </template>
            </el-table-column>
            <el-table-column label="位置X" align="center" prop="height">
              <template slot-scope="scope">
                <el-input v-model="scope.row.height" placeholder="请输入位置X" class="tech-input" @click.native.stop />
              </template>
            </el-table-column>
            <el-table-column label="位置Y" align="center" prop="positionY">
              <template slot-scope="scope">
                <el-input v-model="scope.row.positionY" placeholder="请输入位置Y" class="tech-input" @click.native.stop />
              </template>
            </el-table-column>
            <el-table-column label="槽位状态" align="center" prop="status">
              <template slot-scope="scope">
                <el-select v-model="scope.row.status" placeholder="请选择槽位状态" class="tech-select" @click.native.stop>
                  <el-option label="正常" value="0" @click.stop></el-option>
                  <el-option label="停用" value="1" @click.stop></el-option>
                </el-select>
              </template>
            </el-table-column>
          </el-table>
          <div class="tech-table-buttons" @click.stop>
            <el-button type="primary" icon="el-icon-plus" size="mini" @click.stop="handleAddRmEquipmentSlot" class="tech-button-add">添加</el-button>
            <el-button type="danger" icon="el-icon-delete" size="mini" @click.stop="handleDeleteRmEquipmentSlot" class="tech-button-delete">删除</el-button>
          </div>
        </div>
      </el-form>
      <div slot="footer" class="tech-footer" @click.stop>
        <el-button @click.stop="handleClose" class="tech-button-cancel">取 消</el-button>
        <el-button type="primary" @click.stop="submitForm" class="tech-button-submit">提 交</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listRmRooms } from "@/api/room/RmRoom";
import { listRmCabinets, getRmCabinet } from "@/api/cabinet/RmCabinet";
import { addRmEquipment } from "@/api/equipment/RmEquipment";
import { listRmEquipmentModel, getRmEquipmentModel } from "@/api/model/RmEquipmentModel";
import { generateUUID } from 'three/src/math/MathUtils.js';

export default {
  name: "AddEquipment",
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    selectedModel: {
      type: Object,
      default: () => ({})
    },
    selectedCabinetId: {
      type: String,
      default: ''
    },
    currentClickedNumber: {
      type: Number,
      default: null
    },
    selectedEquipmentType: {
      type: String,
      default: ''
    },
    equipmentList: {
      type: Array,
      default: () => []
    },
    cabinetTotalU: {
      type: Number,
      default: 42
    }
  },
  data() {
    return {
      title: "添加设备",
      // 表单参数
      form: {
        uuid: generateUUID(),
        modelName: "",
        roomName: "",
        cabinetName: "",
        equipmentName: "",
        equipmentCode: "",
        equipmentType: "",
        model: "",
        manufacturer: "",
        length: "",
        width: "",
        height: "",
        startU: "",
        occupiedU: "",
        status: "0",
        modelId: "",
        roomId: "",
        brand: "",
        cabinetId: "",
        deptId: "",
        ipAddress: "",
        rmRoom: null
      },
      // 表单校验
      rules: {
        equipmentName: [
          { required: true, message: "设备名称不能为空", trigger: "blur" }
        ],
        roomName: [
          { required: true, message: "所属机房不能为空", trigger: "change" }
        ]
      },
      // 设备槽位列表
      rmEquipmentSlotList: [],
      // 选中的设备槽位数组
      checkedRmEquipmentSlot: [],
      // 设备模版选项
      modelOptions: [],
      // 机房选项
      roomOptions: [],
      // 机柜选项
      cabinetOptions: []
    };
  },
  created() {
    this.getModelList();
    this.getRoomList();
    this.initFormData();
  },

  mounted() {
    // 确保组件挂载后阻止事件冒泡
    const container = this.$el;
    if (container) {
      container.addEventListener('click', this.stopPropagation);
    }
  },

  beforeDestroy() {
    // 组件销毁前移除事件监听
    const container = this.$el;
    if (container) {
      container.removeEventListener('click', this.stopPropagation);
    }
  },
  methods: {
    // 阻止事件冒泡的方法
    stopPropagation(event) {
      event.stopPropagation();
    },

    // 初始化表单数据
    initFormData() {
      // 如果有选中的模型，填充表单
      if (this.selectedModel && Object.keys(this.selectedModel).length > 0) {
        Object.keys(this.selectedModel).forEach(key => {
          if (this.form.hasOwnProperty(key)) {
            this.form[key] = this.selectedModel[key];
          }
        });
      }

      // 设置设备类型
      if (this.selectedEquipmentType) {
        this.form.equipmentType = this.selectedEquipmentType;
      }

      // 设置起始U位置
      if (this.currentClickedNumber) {
        this.form.startU = this.currentClickedNumber.toString();
      }

      // 设置机柜ID和获取机柜信息
      if (this.selectedCabinetId) {
        this.form.cabinetId = this.selectedCabinetId;
        // 获取机柜信息并设置机柜名称
        this.getCabinetInfo(this.selectedCabinetId);
      }
    },

    // 获取机柜信息
    getCabinetInfo(cabinetId) {
      getRmCabinet(cabinetId).then(response => {
        if (response.data) {
          this.form.cabinetName = response.data.cabinetName;
          // 如果机房ID为空，也设置机房信息
          if (!this.form.roomId) {
            this.form.roomId = response.data.roomId;
            this.form.roomName = response.data.roomName;
          }
        }
      }).catch(error => {
        console.error('获取机柜信息失败:', error);
      });
    },

    // 获取设备模版列表
    getModelList() {
      listRmEquipmentModel().then(response => {
        this.modelOptions = response.rows;
        // console.log('设备模版列表:', this.modelOptions);
      }).catch(error => {
        console.error('获取设备模版失败:', error);
      });
    },

    // 获取设备模版详情及槽位信息
    getModelDetail(modelId) {
      if (!modelId) return;

      const loadingInstance = this.$loading({
        lock: true,
        text: '加载设备模版信息...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });

      getRmEquipmentModel(modelId).then(response => {
        if (response.data) {
          // 填充设备模版相关信息
          this.form.model = response.data.model || '';
          this.form.equipmentType = response.data.equipmentType || '';
          this.form.brand = response.data.brand || '';
          this.form.manufacturer = response.data.manufacturer || '';
          this.form.occupiedU = response.data.occupiedU || '';
          this.form.length = response.data.length || '';
          this.form.width = response.data.width || '';
          this.form.height = response.data.height || '';

          // 填充槽位信息
          if (response.data.rmEquipmentSlotModelList && response.data.rmEquipmentSlotModelList.length > 0) {
            // 清空现有槽位列表
            this.rmEquipmentSlotList = [];

            // 添加从模版获取的槽位信息
            response.data.rmEquipmentSlotModelList.forEach(slot => {
              this.rmEquipmentSlotList.push({
                uuid: "",
                userId: "",
                deptId: "",
                filetime: "",
                remarks: slot.remarks || "",
                slotNumber: slot.slotNumber || "",
                length: slot.length || "",
                width: slot.width || "",
                height: slot.height || "",
                status: slot.status || "0",
                positionX: slot.positionX || "",
                positionY: slot.positionY || ""
              });
            });
          }
        }
        loadingInstance.close();
      }).catch(error => {
        console.error('获取设备模版详情失败:', error);
        loadingInstance.close();
        this.$modal.msgError("获取设备模版详情失败，请重试");
      });
    },

    // 获取机房列表
    getRoomList() {
      listRmRooms().then(response => {
        this.roomOptions = response.rows;
      }).catch(error => {
        console.error('获取机房列表失败:', error);
      });
    },

    // 获取机柜列表
    getCabinetList(roomId) {
      listRmCabinets({ roomId: roomId }).then(response => {
        this.cabinetOptions = response.rows;
      }).catch(error => {
        console.error('获取机柜列表失败:', error);
      });
    },

    // 选择机房
    selectRoom(val) {
      const room = this.roomOptions.find(item => item.roomName === val);
      if (room) {
        this.form.roomId = room.uuid;
        this.getCabinetList(room.uuid);
      }
    },

    // 选择机柜 (此方法已不再使用，机柜信息现在从selectedCabinetId自动获取)
    selectCabinet(val) {
      // 保留此方法以防其他地方调用
      const cabinet = this.cabinetOptions.find(item => item.cabinetName === val);
      if (cabinet) {
        this.form.cabinetId = cabinet.uuid;
      }
    },

    // 处理设备模版选择变化
    handleModelChange(modelId) {
      if (modelId) {
        // 调用获取设备模版详情方法
        this.getModelDetail(modelId);
      } else {
        // 如果清空了选择，则清空槽位列表
        this.rmEquipmentSlotList = [];
      }
    },

    // 设备槽位序号
    rowRmEquipmentSlotIndex({ row, rowIndex }) {
      row.index = rowIndex + 1;
    },

    // 设备槽位选择
    handleRmEquipmentSlotSelectionChange(selection) {
      this.checkedRmEquipmentSlot = selection.map(item => item.index);
    },

    // 添加设备槽位
    handleAddRmEquipmentSlot() {
      let obj = {};
      obj.uuid = "";
      obj.userId = "";
      obj.deptId = "";
      obj.filetime = "";
      obj.remarks = "";
      obj.slotNumber = "";
      obj.length = "";
      obj.width = "";
      obj.height = "";
      obj.status = "0";
      obj.positionX = "";
      obj.positionY = "";
      this.rmEquipmentSlotList.push(obj);
    },

    // 删除设备槽位
    handleDeleteRmEquipmentSlot() {
      if (this.checkedRmEquipmentSlot.length == 0) {
        this.$modal.msgError("请先选择要删除的设备槽位管理数据");
      } else {
        const rmEquipmentSlotList = this.rmEquipmentSlotList;
        const checkedRmEquipmentSlot = this.checkedRmEquipmentSlot;
        this.rmEquipmentSlotList = rmEquipmentSlotList.filter(function (item) {
          return checkedRmEquipmentSlot.indexOf(item.index) == -1;
        });
      }
    },

    // 检查是否有足够的空间放置设备
    checkSpaceAvailability(startU, occupiedU) {
      // 如果没有设置起始U位置或占用U数量，返回false
      if (!startU || !occupiedU) {
        return false;
      }

      // 转换为整数
      const startUInt = parseInt(startU);
      const occupiedUInt = parseInt(occupiedU);

      // 如果没有设备列表，直接返回true
      if (!this.equipmentList || this.equipmentList.length === 0) {
        return true;
      }

      // 获取机柜的总U数
      const totalU = this.cabinetTotalU || 42;

      // 检查是否超出机柜总高度
      if (startUInt + occupiedUInt > totalU) {
        // console.log('设备超出机柜总高度', startUInt, occupiedUInt, totalU);
        return false;
      }

      // 筛选出当前机柜的设备
      const currentCabinetEquipments = this.equipmentList.filter(
        (item) => item.cabinetId === this.selectedCabinetId
      );

      // 检查是否与现有设备重叠
      for (const equipment of currentCabinetEquipments) {
        // 如果是编辑模式且正在编辑的是同一个设备，则跳过
        if (this.form.uuid && equipment.uuid === this.form.uuid) {
          continue;
        }

        const equipStartU = parseInt(equipment.startU);
        const equipOccupiedU = parseInt(equipment.occupiedU);

        // 检查是否有重叠
        // 情况1：新设备的起始位置在现有设备的范围内
        // 情况2：新设备的结束位置在现有设备的范围内
        // 情况3：新设备完全包含现有设备
        if (
          (startUInt >= equipStartU && startUInt < equipStartU + equipOccupiedU) ||
          (startUInt + occupiedUInt > equipStartU && startUInt + occupiedUInt <= equipStartU + equipOccupiedU) ||
          (startUInt <= equipStartU && startUInt + occupiedUInt >= equipStartU + equipOccupiedU)
        ) {
          console.log('设备位置重叠', startUInt, occupiedUInt, equipStartU, equipOccupiedU);
          return false;
        }
      }

      return true;
    },

    // 提交表单
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 检查是否有足够的空间放置设备
          if (!this.form.startU || !this.form.occupiedU) {
            this.$modal.msgError("请填写起始U位置和占用U数量");
            return;
          }

          if (!this.checkSpaceAvailability(this.form.startU, this.form.occupiedU)) {
            const startU = parseInt(this.form.startU);
            const occupiedU = parseInt(this.form.occupiedU);
            const endU = startU + occupiedU - 1;

            if (startU + occupiedU > (this.cabinetTotalU || 42)) {
              this.$modal.msgError(`设备超出机柜范围，当前设置将占用从 ${startU}U 到 ${endU}U，超出机柜总高度 ${this.cabinetTotalU || 42}U`);
            } else {
              this.$modal.msgError(`设备放置位置与现有设备重叠，当前设置将占用从 ${startU}U 到 ${endU}U，请调整起始U位置或占用U数量`);
            }
            return;
          }

          this.form.rmEquipmentSlotList = this.rmEquipmentSlotList;

          const loadingInstance = this.$loading({
            lock: true,
            text: '处理中...',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });

          addRmEquipment(this.form).then(() => {
            loadingInstance.close();
            this.$modal.msgSuccess("添加成功");
            this.$emit('update-equipment-list', {
              startU: parseInt(this.form.startU),
              occupiedU: parseInt(this.form.occupiedU),
              AddEquipmentInfo: this.form
            });
            this.handleClose();
          }).catch(error => {
            loadingInstance.close();
            console.error('添加设备失败:', error);
            this.$modal.msgError("添加失败，请重试");
          });
        }
      });
    },

    // 关闭对话框
    handleClose() {
      this.$emit('update:visible', false);
    }
  }
};
</script>

<style scoped>
.add-equipment-container {
  padding: 10px;
  position: relative;
  z-index: 1002; /* 确保比父组件的z-index高 */
  isolation: isolate; /* 创建新的堆叠上下文，防止事件冒泡 */
}

.tech-dialog /deep/ .el-dialog {
  background: linear-gradient(to bottom, #1a2b42, #0c1a2c);
  border-radius: 8px;
  box-shadow: 0 0 20px rgba(0, 100, 255, 0.3);
  border: 1px solid #1e90ff;
  overflow: hidden;
  position: relative;
  z-index: 1003; /* 确保比容器的z-index高 */
}

.tech-dialog /deep/ .el-dialog__header {
  background: linear-gradient(to right, #0c1a2c, #1e3c72);
  padding: 15px 20px;
  border-bottom: 1px solid #1e90ff;
}

.tech-header {
  display: flex;
  align-items: center;
}

.tech-title {
  color: #1e90ff;
  font-size: 18px;
  font-weight: bold;
  display: flex;
  align-items: center;
}

.tech-icon {
  margin-right: 10px;
  color: #1e90ff;
  font-size: 20px;
}

.tech-dialog /deep/ .el-dialog__body {
  background-color: rgba(12, 26, 44, 0.9);
  color: #e0e0e0;
  padding: 20px;
}

.tech-form-content {
  background-color: rgba(26, 43, 66, 0.5);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  border: 1px solid rgba(30, 144, 255, 0.3);
}

.tech-form /deep/ .el-form-item__label {
  color: #1e90ff;
  font-weight: 500;
}

.tech-input /deep/ .el-input__inner,
.tech-select /deep/ .el-input__inner,
.tech-textarea /deep/ .el-textarea__inner {
  background-color: rgba(12, 26, 44, 0.8);
  border: 1px solid #1e90ff;
  color: #e0e0e0;
  border-radius: 4px;
  height: 40px;
  line-height: 40px;
  font-size: 14px;
}

.tech-input /deep/ .el-input__inner:focus,
.tech-select /deep/ .el-input__inner:focus,
.tech-textarea /deep/ .el-textarea__inner:focus {
  border-color: #1e90ff;
  border-width: 2px;
  box-shadow: 0 0 12px rgba(30, 144, 255, 0.7);
}

/* 增强下拉框箭头样式 */
.tech-select /deep/ .el-input__suffix {
  right: 8px;
}

.tech-select /deep/ .el-input__icon {
  color: #1e90ff;
  font-size: 16px;
  line-height: 40px;
}

.tech-select /deep/ .el-select:hover .el-input__inner {
  border-color: #1e90ff;
  border-width: 2px;
}

.tech-radio /deep/ .el-radio__label {
  color: #e0e0e0;
}

.tech-radio /deep/ .el-radio__input.is-checked .el-radio__inner {
  background-color: #1e90ff;
  border-color: #1e90ff;
}

.tech-radio /deep/ .el-radio__input.is-checked + .el-radio__label {
  color: #1e90ff;
}

.tech-divider {
  display: flex;
  align-items: center;
  margin: 20px 0;
  color: #1e90ff;
  font-weight: bold;
  position: relative;
}

.tech-divider::before,
.tech-divider::after {
  content: '';
  flex: 1;
  height: 1px;
  background: linear-gradient(to right, transparent, #1e90ff, transparent);
}

.tech-divider-text {
  padding: 0 15px;
  position: relative;
}

.tech-divider-text::before {
  content: '◆';
  position: absolute;
  left: -5px;
  top: 50%;
  transform: translateY(-50%);
  color: #1e90ff;
}

.tech-divider-text::after {
  content: '◆';
  position: absolute;
  right: -5px;
  top: 50%;
  transform: translateY(-50%);
  color: #1e90ff;
}

.tech-table-container {
  background-color: rgba(26, 43, 66, 0.5);
  border-radius: 8px;
  padding: 20px;
  border: 1px solid rgba(30, 144, 255, 0.3);
}

.tech-table /deep/ .el-table {
  background-color: transparent;
  color: #e0e0e0;
}

.tech-table /deep/ .el-table th,
.tech-table /deep/ .el-table tr,
.tech-table /deep/ .el-table td {
  background-color: rgba(12, 26, 44, 0.8) !important;
  border-color: #1e90ff !important;
  color: #e0e0e0 !important;
}

.tech-table /deep/ .el-table--striped .el-table__body tr.el-table__row--striped td {
  background-color: rgba(30, 144, 255, 0.1) !important;
}

.tech-table /deep/ .el-table__header-wrapper th {
  background-color: rgba(30, 144, 255, 0.2) !important;
  color: #1e90ff !important;
}

.tech-table /deep/ .el-table::before {
  background-color: transparent !important;
}

.tech-table /deep/ .el-table__empty-block {
  background-color: rgba(12, 26, 44, 0.8) !important;
}

.tech-table /deep/ .el-table__empty-text {
  color: #e0e0e0 !important;
}

.tech-table-buttons {
  margin-top: 15px;
  display: flex;
  gap: 10px;
}

.tech-button-add,
.tech-button-delete {
  border-radius: 4px;
}

.tech-button-add {
  background: linear-gradient(to right, #1e90ff, #4169e1);
  border-color: #1e90ff;
}

.tech-button-delete {
  background: linear-gradient(to right, #ff4757, #ff6b81);
  border-color: #ff4757;
}

.tech-footer {
  text-align: center;
  margin-top: 20px;
}

.tech-button-cancel,
.tech-button-submit {
  width: 120px;
  border-radius: 20px;
  font-weight: bold;
}

.tech-button-cancel {
  background-color: transparent;
  border: 1px solid #1e90ff;
  color: #1e90ff;
}

.tech-button-submit {
  background: linear-gradient(to right, #1e90ff, #4169e1);
  border-color: #1e90ff;
}

.tech-button-submit:hover {
  background: linear-gradient(to right, #4169e1, #1e90ff);
  box-shadow: 0 0 10px rgba(30, 144, 255, 0.5);
}

/* 修复白色背景问题 */
.el-dialog__wrapper {
  background-color: rgba(0, 0, 0, 0.5);
}

.el-select-dropdown {
  background-color: #0c1a2c !important;
  border: 1px solid #1e90ff !important;
  box-shadow: 0 0 10px rgba(30, 144, 255, 0.5) !important;
}

.el-select-dropdown__item {
  color: #070707 !important;
  font-size: 14px !important;
  padding: 8px 12px !important;
  height: auto !important;
  line-height: 1.5 !important;
}

.el-select-dropdown__item.hover,
.el-select-dropdown__item:hover {
  background-color: rgba(30, 144, 255, 0.4) !important;
  color: #050505 !important;
}

.el-select-dropdown__item.selected {
  color: #000000 !important;
  font-weight: bold;
  background-color: rgba(30, 144, 255, 0.6) !important;
}

.el-popper[x-placement^=bottom] .popper__arrow {
  border-bottom-color: #1e90ff !important;
}

.el-popper[x-placement^=bottom] .popper__arrow::after {
  border-bottom-color: #0c1a2c !important;
}

/* 增强下拉菜单样式 */
.el-select-dropdown {
  min-width: 180px !important;
  max-height: 300px !important;
  margin-top: 5px !important;
}

.el-select-dropdown__wrap {
  max-height: 280px !important;
}

.el-scrollbar__bar.is-vertical {
  width: 8px !important;
}

.el-scrollbar__thumb {
  background-color: rgba(30, 144, 255, 0.7) !important;
  border-radius: 4px !important;
}

/* 下拉菜单标题样式 */
.el-select-dropdown__item.is-disabled {
  color: #1e90ff !important;
  font-weight: bold;
  background-color: rgba(30, 144, 255, 0.1) !important;
  cursor: default;
}

.el-radio__inner {
  background-color: rgba(12, 26, 44, 0.8) !important;
  border-color: #1e90ff !important;
}

.el-checkbox__inner {
  background-color: rgba(12, 26, 44, 0.8) !important;
  border-color: #1e90ff !important;
}

.el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: #1e90ff !important;
  border-color: #1e90ff !important;
}

.el-message-box {
  background-color: #0c1a2c !important;
  border: 1px solid #1e90ff !important;
}

.el-message-box__title {
  color: #1e90ff !important;
}

.el-message-box__content {
  color: #e0e0e0 !important;
}

.el-message-box__btns button {
  background: transparent !important;
  border: 1px solid #1e90ff !important;
  color: #1e90ff !important;
}

.el-message-box__btns button:hover {
  background-color: rgba(30, 144, 255, 0.1) !important;
}

.el-message-box__btns button.el-button--primary {
  background: linear-gradient(to right, #1e90ff, #4169e1) !important;
  color: white !important;
}
</style>