import request from '@/utils/request'

// 查询机柜管理列表
export function listRmCabinet(query) {
  return request({
    url: '/cabinet/RmCabinet/list',
    method: 'get',
    params: query
  })
}

// 查询机柜管理详细
export function getRmCabinet(id) {
  return request({
    url: '/cabinet/RmCabinet/' + id,
    method: 'get'
  })
}

// 新增机柜管理
export function addRmCabinet(data) {
  return request({
    url: '/cabinet/RmCabinet',
    method: 'post',
    data: data
  })
}

// 修改机柜管理
export function updateRmCabinet(data) {
  return request({
    url: '/cabinet/RmCabinet',
    method: 'put',
    data: data
  })
}

// 删除机柜管理
export function delRmCabinet(id) {
  return request({
    url: '/cabinet/RmCabinet/' + id,
    method: 'delete'
  })
}

// 查询机柜管理列表
export function listRmCabinets(query) {
  return request({
    url: '/cabinet/RmCabinet/lists',
    method: 'get',
    params: query
  })
}
