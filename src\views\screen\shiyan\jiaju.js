import * as THREE from 'three';
// import {GLTFLoader }from 'three/examples/jsm/loaders/GLTFLoader.js';
import {FBXLoader }from 'three/examples/jsm/loaders/FBXLoader.js';



// 创建椅子腿
const scene = new THREE.Group();
const chairLegGeometry = new THREE.BoxGeometry(1, 8, 1); // 修改变量名为 chairLegGeometry
const legMaterial = new THREE.MeshStandardMaterial({ color: 0x8B4513 });
const leg1 = new THREE.Mesh(chairLegGeometry, legMaterial);
leg1.position.set(-5, 4, -10);
// scene.add(leg1);

const leg2 = leg1.clone();
leg2.position.set(5, 4, -10);
// scene.add(leg2);

// 创建椅子座面
const textureLoader = new THREE.TextureLoader()
const texture = textureLoader.load('./pic/桌子背景图.jpg');
const chairSeatGeometry = new THREE.BoxGeometry(10, 1, 5);
const chairSeatMaterial = new THREE.MeshStandardMaterial({ 
    antialias:true,
    metalness:0.2,
    roughness:0.7,
    // color:0xffffff,
    shininess:1000,
    Clearcoat : 1,
    map: texture,
 });
const chairSeat = new THREE.Mesh(chairSeatGeometry, chairSeatMaterial);
chairSeat.position.set(0, 5.5, -10);
// scene.add(chairSeat);

// 创建椅子靠背
const chairBackGeometry = new THREE.BoxGeometry(1, 6, 5);
const chairBackMaterial = new THREE.MeshStandardMaterial({ color: 0x8B4513 });
const chairBack = new THREE.Mesh(chairBackGeometry, chairBackMaterial);
chairBack.position.set(0, 9.5, -12.5);
// scene.add(chairBack);

// 创建不规则形状的椅子靠背（使用 BufferGeometry）
const vertices = [
    -1, 6, -12, // 顶点 0
    1, 6, -12,  // 顶点 1
    -1, 8, -12, // 顶点 2
    1, 8, -12,  // 顶点 3
    -1, 6, -10, // 顶点 4
    1, 6, -10,  // 顶点 5
    -1, 8, -10, // 顶点 6
    1, 8, -10   // 顶点 7
];

const indices = [
    0, 1, 2, 1, 2, 3, // 前面
    4, 5, 6, 5, 6, 7, // 后面
    0, 1, 4, 1, 4, 5, // 底面
    2, 3, 6, 3, 6, 7, // 顶面
    0, 2, 4, 2, 4, 6, // 左侧面
    1, 3, 5, 3, 5, 7  // 右侧面
];

const chairBackGeometryCustom = new THREE.BufferGeometry();
chairBackGeometryCustom.setAttribute('position', new THREE.Float32BufferAttribute(vertices, 3));
chairBackGeometryCustom.setIndex(indices);

const chairBackCustom = new THREE.Mesh(chairBackGeometryCustom, chairBackMaterial);
scene.position.set(0, 0, 0);
const yizi1 = scene.clone();
const yizi2 = scene.clone();
yizi2.translateX(-50);
const yizi3 = scene.clone();
yizi3.translateX(-100);
const yizi4 = scene.clone();
yizi4.translateX(-150);
const group = new THREE.Group();
group.rotateY(Math.PI);
// group.add(yizi1, yizi2, yizi3);
group.position.set(200, 0, 430);

// 创建桌面
const tableGeometry = new THREE.BoxGeometry(130, 1, 50);
const zhouzitexture2 = new THREE.TextureLoader().load("./pic/桌子/木纹背景图.jpg");
const tableMaterial = new THREE.MeshStandardMaterial({ color: 0x8B4513,
    map:zhouzitexture2,
    antialias:true,
    metalness:0.2,
    roughness:0.7,
    // color:0xffffff,
    shininess:1000,
    ior:1.5,
    Clearcoat:1.0,
    ClearcoatRoughness:0.05,
 });
const table = new THREE.Mesh(tableGeometry, tableMaterial);
table.position.set(0, 0.5, 0);

const zhouzitexture1 = new THREE.TextureLoader().load("./pic/桌子背景图.jpg");
// 创建桌腿
const tableLegGeometry = new THREE.BoxGeometry(5, 20, 5); // 修改变量名为 tableLegGeometry
const tableLegMaterial = new THREE.MeshStandardMaterial({ 
    map: zhouzitexture1,
    // color: 0x8B4513,
    antialias:true,
    metalness:0.2,
    roughness:0.7,
    // color:0xffffff,
    shininess:1000,
    Clearcoat : 1,
 });

const tableLeg1 = new THREE.Mesh(tableLegGeometry, tableLegMaterial);
tableLeg1.position.set(-45, -9.5, -22.5);

const tableLeg2 = new THREE.Mesh(tableLegGeometry, tableLegMaterial);
tableLeg2.position.set(45, -9.5, -22.5);

const tableLeg3 = new THREE.Mesh(tableLegGeometry, tableLegMaterial);
tableLeg3.position.set(-45, -9.5, 22.5);

const tableLeg4 = new THREE.Mesh(tableLegGeometry, tableLegMaterial);
tableLeg4.position.set(45, -9.5, 22.5);

// 创建桌子组
const tableGroup = new THREE.Group();
// tableGroup.add(table, tableLeg1, tableLeg2, tableLeg3, tableLeg4);
tableGroup.position.set(-50, 10, 25);
// group.add(tableGroup);

// const fbxLoad = new FBXLoader();
// fbxLoad.load('./pic/电脑/mac.fbx', function (fbx) {{
//     fbx.rotateY(Math.PI);
//     fbx.translateY(12);
//     // console.log('fbx',fbx);
//     const pingmu = fbx.getObjectByName('对象001');
//     // console.log('pingmu',pingmu);
//     textureLoader.load('./pic/电脑/a665a4c4ca2520fd1165ff890ac3d5e.jpg', function (texture) {
//         pingmu.material.map = texture;
//         pingmu.material.shininess=100;
//         pingmu.material.color.setHex( 0xFFFFFF);
//     });
//     const fbx1 = fbx.clone();
//     fbx1.translateX(55);
//     const fbx2 = fbx.clone();
//     fbx2.translateX(110);
//     // group.add(fbx,fbx1,fbx2);
//     }
// });
// // shiyan\pic\办公人员\ren.jpg
// // E:\WorkSpace_E\Three.js视频教程源码b站\myThreejs\shiyan\pic\办公人员\ren.jpg
// fbxLoad.load('./pic/办公人员/zuozhe.fbx', function (fbx) {
//     // fbx.rotateY(Math.PI);
//     fbx.scale.set(0.015,0.015,0.015);
//     fbx.translateZ(-4);
//     fbx.translateY(-1);
//     textureLoader.load('./pic/办公人员/ren.jpg',function (texture) {
//         fbx.children.forEach(child=>{
//             child.material.map = texture;
//             child.material.shininess=100;
//         })
//     });
//     const fbx1 = fbx.clone();
//     const fbx2 = fbx.clone();
//     fbx1.translateX(-50);
//     fbx2.translateX(-100);
//     // fbx.translateY(50);
//     // console.log('fbx',fbx);

//     // group.add(fbx,fbx1,fbx2);
// });
// const axisHelper = new THREE.AxesHelper(1000);
// group.add(axisHelper);
export default group;