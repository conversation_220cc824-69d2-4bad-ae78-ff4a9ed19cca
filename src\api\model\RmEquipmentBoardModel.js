import request from '@/utils/request'

// 查询设备板卡模板管理列表
export function listRmEquipmentBoardModel(query) {
  return request({
    url: '/model/RmEquipmentBoardModel/list',
    method: 'get',
    params: query
  })
}

// 查询设备板卡模板管理详细
export function getRmEquipmentBoardModel(id) {
  return request({
    url: '/model/RmEquipmentBoardModel/' + id,
    method: 'get'
  })
}

// 新增设备板卡模板管理
export function addRmEquipmentBoardModel(data) {
  return request({
    url: '/model/RmEquipmentBoardModel',
    method: 'post',
    data: data
  })
}

// 修改设备板卡模板管理
export function updateRmEquipmentBoardModel(data) {
  return request({
    url: '/model/RmEquipmentBoardModel',
    method: 'put',
    data: data
  })
}

// 删除设备板卡模板管理
export function delRmEquipmentBoardModel(id) {
  return request({
    url: '/model/RmEquipmentBoardModel/' + id,
    method: 'delete'
  })
}

// 查询设备板卡模板管理列表
export function listRmEquipmentBoardModels(query) {
  return request({
    url: '/model/RmEquipmentBoardModel/lists',
    method: 'get',
    params: query
  })
}
