<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="120px">
      <el-form-item label="所属区域" prop="deptId">
        <treeselect v-model="queryParams.deptId" :options="deptOptions" :show-count="true" placeholder="请选择所属区域"
          style="width:240px" />
      </el-form-item>
      <el-form-item label="所属机柜" prop="cabinetName">
        <el-input v-model="queryParams.cabinetName" placeholder="请输入所属机柜" clearable @keyup.enter.native="handleQuery"
          style="width:240px" />
      </el-form-item>
      <el-form-item label="所属机房" prop="roomName">
        <el-input v-model="queryParams.roomName" placeholder="请输入所属机房" clearable @keyup.enter.native="handleQuery"
          style="width:240px" />
      </el-form-item>
      <el-form-item label="设备名称" prop="equipmentName">
        <el-input v-model="queryParams.equipmentName" placeholder="请输入设备名称" clearable @keyup.enter.native="handleQuery"
          style="width:240px" />
      </el-form-item>
      <el-form-item label="设备编码" prop="equipmentCode">
        <el-input v-model="queryParams.equipmentCode" placeholder="请输入设备编码" clearable @keyup.enter.native="handleQuery"
          style="width:240px" />
      </el-form-item>
      <el-form-item label="设备类型" prop="equipmentType">
        <el-select v-model="queryParams.equipmentType" placeholder="请选择设备类型" clearable style="width:240px">
          <el-option v-for="dict in dict.type.equipment_type" :key="dict.value" :label="dict.label"
            :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="设备型号" prop="model">
        <el-input v-model="queryParams.model" placeholder="请输入设备型号" clearable @keyup.enter.native="handleQuery"
          style="width:240px" />
      </el-form-item>
      <el-form-item label="设备品牌" prop="brand">
        <el-input v-model="queryParams.brand" placeholder="请输入设备品牌" clearable @keyup.enter.native="handleQuery"
          style="width:240px" />
      </el-form-item>
      <el-form-item label="购置日期">
        <el-date-picker v-model="daterangePurchaseDate" style="width: 240px" value-format="yyyy-MM-dd" type="daterange"
          range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
      </el-form-item>
      <!-- <el-form-item label="设备所在机柜起始U的位置" prop="startU">
        <el-input
          v-model="queryParams.startU"
          placeholder="请输入设备所在机柜起始U的位置"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="设备占用U的数量" prop="occupiedU">
        <el-input
          v-model="queryParams.occupiedU"
          placeholder="请输入设备占用U的数量"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="长度" prop="length">
        <el-input
          v-model="queryParams.length"
          placeholder="请输入长度"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="宽度" prop="width">
        <el-input
          v-model="queryParams.width"
          placeholder="请输入宽度"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="高度" prop="height">
        <el-input
          v-model="queryParams.height"
          placeholder="请输入高度"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="设备IP地址" prop="ipAddress">
        <el-input v-model="queryParams.ipAddress" placeholder="请输入设备IP地址" clearable @keyup.enter.native="handleQuery"
          style="width:240px" />
      </el-form-item>
      <el-form-item label="设备状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择设备状态" clearable style="width:240px">
          <el-option v-for="dict in dict.type.equipment_status" :key="dict.value" :label="dict.label"
            :value="dict.value" />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="所在机柜位置x" prop="positionX">
        <el-input
          v-model="queryParams.positionX"
          placeholder="请输入所在机柜位置x"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="所在机柜位置y" prop="positionY">
        <el-input
          v-model="queryParams.positionY"
          placeholder="请输入所在机柜位置y"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['equipment:RmEquipment:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
          v-hasPermi="['equipment:RmEquipment:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDeletes"
          v-hasPermi="['equipment:RmEquipment:removes']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
          v-hasPermi="['equipment:RmEquipment:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="RmEquipmentList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column prop="" label="序号" width="80" align="center" type="index" :index="indexMethod" />
      <el-table-column label="所属区域" align="center" prop="deptId" />
      <el-table-column label="所属机房" align="center" prop="roomName" />
      <el-table-column label="所属机柜" align="center" prop="cabinetName" />
      <el-table-column label="设备名称" align="center" prop="equipmentName" />
      <el-table-column label="设备编码" align="center" prop="equipmentCode" />
      <el-table-column label="设备类型" align="center" prop="equipmentType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.equipment_type" :value="scope.row.equipmentType" />
        </template>
      </el-table-column>
      <el-table-column label="设备型号" align="center" prop="model" />
      <!-- <el-table-column label="设备品牌" align="center" prop="brand" /> -->
      <!-- <el-table-column label="设备购置日期" align="center" prop="purchaseDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.purchaseDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="设备所在机柜起始U的位置" align="center" prop="startU" />
      <el-table-column label="设备占用U的数量" align="center" prop="occupiedU" />
      <el-table-column label="长度" align="center" prop="length" />
      <el-table-column label="宽度" align="center" prop="width" />
      <el-table-column label="高度" align="center" prop="height" /> -->
      <el-table-column label="设备 IP 地址" align="center" prop="ipAddress" />
      <el-table-column label="设备状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.equipment_status" :value="scope.row.status" />
        </template>
      </el-table-column>
      <!-- <el-table-column label="所在机柜位置x" align="center" prop="positionX" />
      <el-table-column label="所在机柜位置y" align="center" prop="positionY" /> -->
      <!-- <el-table-column label="备注说明" align="center" prop="remarks" /> -->
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <!-- <el-button size="mini" type="text" icon="el-icon-edit" @click="EquipmentBoard(scope.row)"
            v-hasPermi="['equipment:RmEquipment:edit']">板卡管理</el-button> -->
          <el-button size="mini" type="text" icon="el-icon-search" @click="handleView(scope.row)"
            v-hasPermi="['equipment:RmEquipment:edit']">查看</el-button>
          <el-dropdown>
            <span class="el-dropdown-link" style="padding:5px 10px;">
              <i class="el-icon-setting"></i>
              编辑
              <i class="el-icon-arrow-down el-icon--right"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item>
                <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
                  v-hasPermi="['equipment:RmEquipment:edit']">修改</el-button>
              </el-dropdown-item>
              <el-dropdown-item>
                <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                  v-hasPermi="['equipment:RmEquipment:remove']">删除</el-button>
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改设备管理对话框 -->
    <el-dialog :title="title" :fullscreen="dialogFull" :visible.sync="open" width="80%" append-to-body
      :close-on-click-modal="false" @close="handleClose()">
      <template slot="title">
        <div class="avue-crud__dialog__header" style="border-bottom:1px solid #DCDFE6;padding-bottom:15px;">
          <span class="el-dialog__title">
            <span
              style="display:inline-block;background-color: #3478f5;width:3px;height:20px;margin-right:5px; float: left;margin-top:2px;margin-right:15px"></span>
            {{ title }}
          </span>
          <div class="avue-crud__dialog__menu" @click="dialogFull ? dialogFull = false : dialogFull = true">
            <i class="el-icon-full-screen"></i>
          </div>
        </div>
      </template>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="8">
            <el-form-item label="设备模板" prop="modelName">
              <!-- <el-input v-model="form.deptId" placeholder="请输入所属区域" /> -->
              <el-select v-model="form.modelName" placeholder="请选择模板" filterable clearable @change="selectModel"
                style="width:100%">
                <el-option v-for="(item, index) in RmEquipmentModelList" :key="index" :label="item.equipmentName"
                  :value="item.uuid">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="所属机房" prop="roomName">
              <!-- <el-input v-model="form.roomName" placeholder="请输入所属机房" /> -->
              <el-select v-model="form.roomName" placeholder="请选所属机房" filterable clearable @change="selectRoom"
                v-if="showSearch" style="width:100%">
                <el-option v-for="(item, index) in RmRoomList" :key="index" :label="item.roomName" :value="item.uuid">
                </el-option>
              </el-select>
              <!-- <el-select v-model="form.roomName" placeholder="请选所属机房" filterable clearable @change="selectRoom"
                v-else style="width:100%" readonly>
                <el-option v-for="(item, index) in RmRoomList" :key="index" :label="item.roomName" :value="item.uuid">
                </el-option>
              </el-select> -->
              <el-input v-else v-model="form.roomName" placeholder="请输入所属机房" readonly />
              <!-- <div v-else>{{ form.roomName }}</div> -->
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="所属机柜" prop="cabinetName">
              <!-- <el-input v-model="form.cabinetName" placeholder="请输入所属机柜" /> -->
              <el-select v-model="form.cabinetName" placeholder="请选择机柜" filterable clearable @change="selectCabinet">
                <el-option v-for="(item, index) in RmCabinetList" :key="index" :label="item.cabinetName"
                  :value="item.uuid">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="设备名称" prop="equipmentName">
              <el-input v-model="form.equipmentName" placeholder="请输入设备名称" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="设备编码" prop="equipmentCode">
              <el-input v-model="form.equipmentCode" placeholder="请输入设备编码" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="设备 IP 地址" prop="ipAddress">
              <el-input v-model="form.ipAddress" placeholder="请输入设备 IP 地址" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="设备类型" prop="equipmentType">
              <el-select v-model="form.equipmentType" placeholder="请选择设备类型" style="width:100%">
                <el-option v-for="dict in dict.type.equipment_type" :key="dict.value" :label="dict.label"
                  :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="设备型号" prop="model">
              <el-input v-model="form.model" placeholder="请输入设备型号" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="设备品牌" prop="brand">
              <el-input v-model="form.brand" placeholder="请输入设备品牌" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="长度(mm)" prop="length">
              <el-input v-model="form.length" placeholder="请输入长度" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="宽度(mm)" prop="width">
              <el-input v-model="form.width" placeholder="请输入宽度" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="高度(mm)" prop="height">
              <el-input v-model="form.height" placeholder="请输入高度" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="购置日期" prop="purchaseDate">
              <el-date-picker clearable v-model="form.purchaseDate" type="date" value-format="yyyy-MM-dd"
                placeholder="请选择购置日期" style="width:100%">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="起始U的位置" prop="startU">
              <el-input v-model="form.startU" placeholder="请输入起始U的位置" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="占用U的数量" prop="occupiedU">
              <el-input v-model="form.occupiedU" placeholder="请输入占用U的数量" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="位置x(mm)" prop="positionX">
              <el-input v-model="form.positionX" placeholder="请输入所在机柜位置x" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="位置y(mm)" prop="positionY">
              <el-input v-model="form.positionY" placeholder="请输入所在机柜位置y" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="设备状态">
              <el-radio-group v-model="form.status">
                <el-radio v-for="dict in dict.type.equipment_status" :key="dict.value" :label="dict.value">{{ dict.label
                  }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注说明" prop="remarks">
              <el-input v-model="form.remarks" type="textarea" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-divider content-position="center">设备槽位管理信息</el-divider>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAddRmEquipmentSlot">添加</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" icon="el-icon-delete" size="mini"
              @click="handleDeleteRmEquipmentSlot">删除</el-button>
          </el-col>
        </el-row>
        <el-table :data="rmEquipmentSlotList" :row-class-name="rowRmEquipmentSlotIndex"
          @selection-change="handleRmEquipmentSlotSelectionChange" ref="rmEquipmentSlot" stripe border>
          <el-table-column type="selection" width="50" align="center" />
          <el-table-column label="序号" align="center" prop="index" width="50" />
          <el-table-column label="槽位编号" prop="slotNumber" align="center">
            <template slot-scope="scope">
              <el-input v-model="scope.row.slotNumber" placeholder="请输入槽位编号" />
            </template>
          </el-table-column>
          <el-table-column label="长度" prop="length" align="center">
            <template slot-scope="scope">
              <el-input v-model="scope.row.length" placeholder="请输入长度" />
            </template>
          </el-table-column>
          <el-table-column label="宽度" prop="width" align="center">
            <template slot-scope="scope">
              <el-input v-model="scope.row.width" placeholder="请输入宽度" />
            </template>
          </el-table-column>
          <el-table-column label="高度" prop="height" align="center">
            <template slot-scope="scope">
              <el-input v-model="scope.row.height" placeholder="请输入高度" />
            </template>
          </el-table-column>
          <el-table-column label="位置X" prop="positionX" align="center">
            <template slot-scope="scope">
              <el-input v-model="scope.row.positionX" placeholder="请输入位置X" />
            </template>
          </el-table-column>
          <el-table-column label="位置Y" prop="positionY" align="center">
            <template slot-scope="scope">
              <el-input v-model="scope.row.positionY" placeholder="请输入位置Y" />
            </template>
          </el-table-column>
          <el-table-column label="槽位状态" prop="status" align="center">
            <template slot-scope="scope">
              <el-select v-model="scope.row.status" placeholder="请选择槽位状态">
                <el-option v-for="dict in dict.type.slot_status" :key="dict.value" :label="dict.label"
                  :value="dict.value"></el-option>
              </el-select>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="savesubmitForm">保存</el-button>
        <el-button type="primary" @click="submitForm">提 交</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 添加或修改板卡管理对话框 -->
    <el-dialog :title="title" :fullscreen="dialogFull" :visible.sync="openCab" width="70%" append-to-body
      :close-on-click-modal="false" @close="handleClose()">
      <template slot="title">
        <div class="avue-crud__dialog__header" style="border-bottom:1px solid #DCDFE6;padding-bottom:15px;">
          <span class="el-dialog__title">
            <span
              style="display:inline-block;background-color: #3478f5;width:3px;height:20px;margin-right:5px; float: left;margin-top:2px;margin-right:15px"></span>
            {{ title }}
          </span>
          <div class="avue-crud__dialog__menu" @click="dialogFull ? dialogFull = false : dialogFull = true">
            <i class="el-icon-full-screen"></i>
          </div>
        </div>
      </template>
      <RmEquipmentBoard :quipmentInfo="quipmentInfo"></RmEquipmentBoard>
    </el-dialog>

    <!-- 查看设备管理对话框 -->
    <el-dialog :title="title" :fullscreen="dialogFull" :visible.sync="openshow" width="80%" append-to-body
      :close-on-click-modal="false" @close="handleClose()">
      <template slot="title">
        <div class="avue-crud__dialog__header" style="border-bottom:1px solid #DCDFE6;padding-bottom:15px;">
          <span class="el-dialog__title">
            <span
              style="display:inline-block;background-color: #3478f5;width:3px;height:20px;margin-right:5px; float: left;margin-top:2px;margin-right:15px"></span>
            {{ title }}
          </span>
          <div class="avue-crud__dialog__menu" @click="dialogFull ? dialogFull = false : dialogFull = true">
            <i class="el-icon-full-screen"></i>
          </div>
        </div>
      </template>
      <div class="content">
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane label="基本信息" name="first">
            <el-form ref="form" :model="form" :rules="rules" label-width="120px">
              <el-row>
                <el-col :span="8">
                  <el-form-item label="所属区域：" prop="deptId">
                    <treeselect v-model="form.deptId" :options="deptOptions" :show-count="true" placeholder="请选择所属区域"
                      style="width:100%" disabled />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="所属机房：" prop="roomName">
                    <el-input v-model="form.roomName" placeholder="请输入所属机房" readonly />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="所属机柜：" prop="cabinetName">
                    <el-input v-model="form.cabinetName" placeholder="请输入所属机柜：" readonly />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="8">
                  <el-form-item label="设备名称：" prop="equipmentName">
                    <el-input v-model="form.equipmentName" placeholder="请输入设备名称" readonly />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="设备编码：" prop="equipmentCode">
                    <el-input v-model="form.equipmentCode" placeholder="请输入设备编码" readonly />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="设备IP地址：" prop="ipAddress">
                    <el-input v-model="form.ipAddress" placeholder="请输入设备IP地址" readonly />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="8">
                  <el-form-item label="设备类型：" prop="equipmentType">
                    <el-select v-model="form.equipmentType" placeholder="请选择设备类型" style="width:100%" disabled>
                      <el-option v-for="dict in dict.type.equipment_type" :key="dict.value" :label="dict.label"
                        :value="dict.value"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="设备型号：" prop="model">
                    <el-input v-model="form.model" placeholder="请输入设备型号" readonly />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="设备品牌：" prop="brand">
                    <el-input v-model="form.brand" placeholder="请输入设备品牌" readonly />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="8">
                  <el-form-item label="长度(mm)：" prop="length">
                    <el-input v-model="form.length" placeholder="请输入长度" readonly />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="宽度(mm)：" prop="width">
                    <el-input v-model="form.width" placeholder="请输入宽度" readonly />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="高度(mm)：" prop="height">
                    <el-input v-model="form.height" placeholder="请输入高度" readonly />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="8">
                  <el-form-item label="购置日期：" prop="purchaseDate">
                    <el-input v-model="form.purchaseDate" placeholder="请输入起始U的位置" readonly />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="起始U的位置：" prop="startU">
                    <el-input v-model="form.startU" placeholder="请输入起始U的位置" readonly />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="占用U的数量：" prop="occupiedU">
                    <el-input v-model="form.occupiedU" placeholder="请输入占用U的数量" readonly />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="8">
                  <el-form-item label="位置x(mm)：" prop="positionX">
                    <el-input v-model="form.positionX" placeholder="请输入所在机柜位置x" readonly />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="位置y(mm)：" prop="positionY">
                    <el-input v-model="form.positionY" placeholder="请输入所在机柜位置y" readonly />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="设备状态：">
                    <dict-tag :options="dict.type.equipment_status" :value="form.status" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <el-form-item label="备注说明：" prop="remarks">
                    <el-input v-model="form.remarks" type="textarea" placeholder="请输入内容" readonly />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-divider content-position="center">设备槽位管理信息</el-divider>
              <!-- <el-row :gutter="10" class="mb8">
              <el-col :span="1.5">
                <el-button type="primary" icon="el-icon-plus" size="mini"
                  @click="handleAddRmEquipmentBoards">批量添加</el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button type="danger" icon="el-icon-delete" size="mini"
                  @click="handleDeleteRmEquipmentSlot">删除</el-button>
              </el-col>
            </el-row> -->
              <el-table :data="rmEquipmentSlotList" :row-class-name="rowRmEquipmentSlotIndex"
                @selection-change="handleRmEquipmentSlotSelectionChange" ref="rmEquipmentSlot" stripe border>
                <el-table-column type="selection" width="50" align="center" />
                <el-table-column label="序号" align="center" prop="index" width="50" />
                <el-table-column label="槽位编号" prop="slotNumber" align="center"></el-table-column>
                <el-table-column label="长度" prop="length" align="center"></el-table-column>
                <el-table-column label="宽度" prop="width" align="center"></el-table-column>
                <el-table-column label="高度" prop="height" align="center"></el-table-column>
                <el-table-column label="位置X" prop="positionX" align="center"></el-table-column>
                <el-table-column label="位置Y" prop="positionY" align="center"></el-table-column>
                <el-table-column label="槽位状态" prop="status" align="center">
                  <template slot-scope="scope">
                    <dict-tag :options="dict.type.slot_status" :value="scope.row.status" />
                  </template>
                </el-table-column>
                <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                  <template slot-scope="scope">
                    <el-button size="mini" type="text" icon="el-icon-edit"
                    @click="handleViewBoard(scope.row)">查看</el-button>
                    <el-button size="mini" type="text" icon="el-icon-edit"
                      @click="handleAddBoard(scope.row)">添加</el-button>
                    <!-- <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAddRmEquipmentSlot">添加</el-button> -->
                  </template>
                </el-table-column>
              </el-table>
            </el-form>
          </el-tab-pane>
          <el-tab-pane label="板卡管理" name="second">
            <RmEquipmentBoard :quipmentInfo="quipmentInfo" ref="rmEquipmentBoard" @boardAdded="refreshSlotStatus"></RmEquipmentBoard>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-dialog>

     <!-- 查看设备槽位板卡管理对话框 -->
     <el-dialog :title="title" :fullscreen="dialogFullboard" :visible.sync="openshowborad" width="80%" append-to-body
      :close-on-click-modal="false" @close="handleCloseboard()">
      <template slot="title">
        <div class="avue-crud__dialog__header" style="border-bottom:1px solid #DCDFE6;padding-bottom:15px;">
          <span class="el-dialog__title">
            <span
              style="display:inline-block;background-color: #3478f5;width:3px;height:20px;margin-right:5px; float: left;margin-top:2px;margin-right:15px"></span>
            {{ title }}
          </span>
          <div class="avue-crud__dialog__menu" @click="dialogFullboard ? dialogFullboard = false : dialogFullboard = true">
            <i class="el-icon-full-screen"></i>
          </div>
        </div>
      </template>
      <div class="content">
          <RmEquipmentBoard :quipmentInfo="quipmentInfo" ref="rmEquipmentBoard" @boardAdded="refreshSlotStatus"></RmEquipmentBoard>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listRmEquipment, getRmEquipment, delRmEquipment, addRmEquipment, updateRmEquipment } from "@/api/equipment/RmEquipment";
import { listRmRooms } from "@/api/room/RmRoom";
import { listRmCabinets } from "@/api/cabinet/RmCabinet";
import { deptTreeSelect } from "@/api/system/user";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import RmEquipmentBoard from "@/views/equipment/RmEquipmentBoard/index.vue";
import { listRmEquipmentModels, getRmEquipmentModel } from "@/api/model/RmEquipmentModel";

export default {
  name: "RmEquipment",
  dicts: ['equipment_status', 'equipment_type', 'sys_normal_disable', 'slot_status'],
  components: { Treeselect, RmEquipmentBoard },
  props: {
    roomInfo: {
      type: Object,
      default: () => ({}) // 默认值为空对象
    }
  },
  data() {
    return {
      activeName: 'first',
      //是否弹窗全屏
      dialogFull: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 子表选中数据
      checkedRmEquipmentSlot: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 设备管理表格数据
      RmEquipmentList: [],
      // 设备槽位管理表格数据
      rmEquipmentSlotList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 位置Y时间范围
      daterangePurchaseDate: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        deptId: null,
        remarks: null,
        equipmentName: null,
        equipmentCode: null,
        equipmentType: null,
        model: null,
        brand: null,
        purchaseDate: null,
        startU: null,
        occupiedU: null,
        length: null,
        width: null,
        height: null,
        ipAddress: null,
        status: null,
        positionX: null,
        positionY: null,
        cabinetName: null,
        roomName: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        equipmentName: [
          { required: true, message: "设备名称不能为空", trigger: "blur" }
        ],
        equipmentCode: [
          { required: true, message: "设备编码不能为空", trigger: "blur" }
        ],
        equipmentType: [
          { required: true, message: "设备类型不能为空", trigger: "change" }
        ],
      },

      deptOptions: undefined,
      // datas: {},
      // roomInfo: {},
      // 机柜模板管理表格数据
      RmCabinetList: [],
      // 查询参数
      RoomqueryParams: {
        pageNum: 1,
        pageSize: 10,
        status: null,
        remarks: null,
        deptId: null,
        roomName: null,
        roomCode: null,
        address: null,
        length: null,
        width: null,
        height: null,
        longitude: null,
        latitud: null,
        floorCount: null,
        roomLevel: null,
        modelId: null,
        modelName: null
      },
      // 查询参数
      CabinetqueryParams: {
        roomId: null,
        roomName: null,
      },
      // 机房管理表格数据
      RmRoomList: [],
      // 设备模板管理表格数据
      RmEquipmentModelList: [],
      // 设备槽位模板管理表格数据
      rmEquipmentSlotModelList: [],
      quipmentInfo: {},
      openshow: false,
      openCab: false,
      openshowborad:false,
      dialogFullboard:false,
      // openBoardDialogVisible:false,
    };
  },
  created() {
    if (Object.keys(this.roomInfo).length > 0) {
      this.showSearch = false;
      this.queryParams.roomId = this.roomInfo.uuid;
      this.queryParams.roomName = this.roomInfo.roomName;
      this.form.roomId = this.roomInfo.uuid;
      this.form.roomName = this.roomInfo.roomName;
      this.getCabinetList(this.roomInfo.uuid)
    }
    this.getList();
    this.getDeptTree();
  },
  watch: {
    roomInfo: {
      deep: true,
      handler() {
        this.showSearch = false;
        this.queryParams.roomId = this.roomInfo.uuid;
        this.queryParams.roomName = this.roomInfo.roomName;
        this.form.roomId = this.roomInfo.uuid;
        this.form.roomName = this.roomInfo.roomName;
        this.getCabinetList(this.roomInfo.uuid)
        this.getList();
        this.getDeptTree();
      }
    },
  },
  methods: {
    handleAddBoard(row) {
      this.quipmentInfo = this.form;
      this.$refs.rmEquipmentBoard.openDialog(row);
    },
    handleViewBoard(row) {
      this.dialogFullboard=false,
      this.openshowborad=true;
      this.quipmentInfo = this.form;
      this.$refs.rmEquipmentBoard.openviewDialog(row);
    },
    handleCloseboard(){
      this.openshowborad=false;
      this.quipmentInfo.slotId=null;
    },
    handleAddRmEquipmentBoards(row) {
      console.log("slot===", row);
    },
    refreshSlotStatus() {
      // 刷新槽位状态的逻辑
      const id = this.form.uuid;
      getRmEquipment(id).then(response => {
        this.rmEquipmentSlotList = response.data.rmEquipmentSlotList;
        // this.$message.success('槽位状态已刷新');
      }).catch(() => {
        this.$message.error('刷新槽位状态失败');
      });
    },
    /** 设备板卡编辑*/
    EquipmentBoard(val) {
      this.openCab = true;
      this.dialogFull = true;
      this.title = "设备板卡理-" + val.equipmentName;
      this.quipmentInfo = val;
    },
    /** 查询部门下拉树结构 */
    getDeptTree() {
      deptTreeSelect().then(response => {
        this.deptOptions = response.data;
      });
    },
    indexMethod(index) {
      return (
        index + (this.queryParams.pageNum - 1) * this.queryParams.pageSize + 1
      );
    },
    handleChange(val) {
      // console.log(val);
    },
    handleClick(tab, event) {
      // console.log(tab, event);
    },
    handleClose() {
      this.cancel();
    },
    /** 查询设备管理列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangePurchaseDate && '' != this.daterangePurchaseDate) {
        this.queryParams.params["beginPurchaseDate"] = this.daterangePurchaseDate[0];
        this.queryParams.params["endPurchaseDate"] = this.daterangePurchaseDate[1];
      }
      listRmEquipment(this.queryParams).then(response => {
        this.RmEquipmentList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.openshow = false;
      this.dialogFull = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        uuid: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        userId: null,
        deptId: null,
        filetime: null,
        remarks: null,
        delFlag: null,
        cabinetId: null,
        equipmentName: null,
        equipmentCode: null,
        equipmentType: null,
        model: null,
        brand: null,
        purchaseDate: null,
        startU: null,
        occupiedU: null,
        length: null,
        width: null,
        height: null,
        ipAddress: null,
        status: "1",
        positionX: null,
        positionY: null,
        cabinetName: null,
        roomId: null,
        roomName: null,
        modelname: null,
        modelId: null,
        modelName: null
      };
      this.rmEquipmentSlotList = [];
      this.activeName = 'first';
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangePurchaseDate = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.uuid)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加";
      this.getRoomList();
      this.getModelList();
      if (Object.keys(this.roomInfo).length > 0) {
        // this.showSearch = false;
        this.dialogFull = false;
        this.form.roomName = this.roomInfo.roomName;
        this.form.roomId = this.roomInfo.uuid;
      } else {
        this.dialogFull = true;
      }
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.uuid || this.ids
      getRmEquipment(id).then(response => {
        this.form = response.data;
        this.rmEquipmentSlotList = response.data.rmEquipmentSlotList;
        this.open = true;
        this.title = "修改";
        this.getRoomList();
        this.getModelList();
        if (Object.keys(this.roomInfo).length > 0) {
          // this.showSearch = false;
          this.dialogFull = false;
          // this.form.roomName = this.roomInfo.roomName;
          // this.form.roomId = this.roomInfo.uuid;
        } else {
          this.dialogFull = true;
        }
      });
    },
    handleView(row) {
      this.reset();
      const id = row.uuid || this.ids
      getRmEquipment(id).then(response => {
        this.form = response.data;
        this.rmEquipmentSlotList = response.data.rmEquipmentSlotList;
        this.openshow = true;
        this.title = "查看";
        this.getRoomList();
        this.getModelList();
        if (Object.keys(this.roomInfo).length > 0) {
          // this.showSearch = false;
          this.dialogFull = false;
          // this.form.roomName = this.roomInfo.roomName;
          // this.form.roomId = this.roomInfo.uuid;
          //  this.openshowborad=true;
          console.log("aaaaaa");
        } else {
          this.dialogFull = true;
              console.log("bbbb");
        }
        this.quipmentInfo = row;
        this.$refs.rmEquipmentBoard.openviewDialog(row);
        
      });
      this.quipmentInfo = row;
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.form.rmEquipmentSlotList = this.rmEquipmentSlotList;
          if (this.form.uuid != null) {
            updateRmEquipment(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addRmEquipment(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 保存按钮 */
    savesubmitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.form.rmEquipmentSlotList = this.rmEquipmentSlotList;
          addRmEquipment(this.form).then(response => {
            this.$modal.msgSuccess("保存成功");
            // this.open = false;
            this.getList();
          });
        }
      });
    },
    /** 删除按钮操作 */
    handleDeletes(row) {
      const ids = row.uuid || this.ids;
      this.$modal.confirm('是否确认删除选中的数据项？').then(function () {
        return delRmEquipment(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.uuid || this.ids;
      this.$modal.confirm('是否确认删除数据？').then(function () {
        return delRmEquipment(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 设备槽位管理序号 */
    rowRmEquipmentSlotIndex({ row, rowIndex }) {
      row.index = rowIndex + 1;
    },
    /** 设备槽位管理添加按钮操作 */
    handleAddRmEquipmentSlot() {
      let obj = {};
      obj.uuid = "";
      obj.userId = "";
      obj.deptId = "";
      obj.filetime = "";
      obj.remarks = "";
      obj.slotNumber = "";
      obj.length = "";
      obj.width = "";
      obj.height = "";
      obj.status = "0";
      obj.positionX = "";
      obj.positionY = "";
      this.rmEquipmentSlotList.push(obj);
    },
    /** 设备槽位管理删除按钮操作 */
    handleDeleteRmEquipmentSlot() {
      if (this.checkedRmEquipmentSlot.length == 0) {
        this.$modal.msgError("请先选择要删除的设备槽位管理数据");
      } else {
        const rmEquipmentSlotList = this.rmEquipmentSlotList;
        const checkedRmEquipmentSlot = this.checkedRmEquipmentSlot;
        this.rmEquipmentSlotList = rmEquipmentSlotList.filter(function (item) {
          return checkedRmEquipmentSlot.indexOf(item.index) == -1
        });
      }
    },
    /** 复选框选中数据 */
    handleRmEquipmentSlotSelectionChange(selection) {
      this.checkedRmEquipmentSlot = selection.map(item => item.index)
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('equipment/RmEquipment/export', {
        ...this.queryParams
      }, `导出明细_${new Date().getTime()}.xlsx`)
    },

    /** 查询机房管理列表 */
    getRoomList() {
      this.loading = true;
      listRmRooms().then(response => {
        this.RmRoomList = response.rows;
        // this.total = response.total;
        this.loading = false;
      });
    },
    selectRoom(val) {
      const RoomMode = this.RmRoomList.find(item => item.uuid === val)
      // console.log("this.form.roomName===", this.form.roomName);
      //重新选择机房，自动更新所属机柜
      if (this.form.roomName != RoomMode.roomName || this.form.roomName == null) {
        // console.log("this.form.cabinetName===", this.form.cabinetName);
        this.form.cabinetName = "";
        this.form.cabinetId = "";
        this.RmCabinetList = [];
        this.getCabinetList(RoomMode.uuid);
      }
      this.form.roomName = RoomMode.roomName
      this.form.roomId = RoomMode.uuid
    },
    /** 查询机房机柜管理列表 */
    getCabinetList(val) {
      this.loading = true;
      this.CabinetqueryParams.roomId = val;
      listRmCabinets(this.CabinetqueryParams).then(response => {
        this.RmCabinetList = response.rows;
        this.loading = false;
      });
    },
    selectCabinet(val) {
      const RoomMode = this.RmCabinetList.find(item => item.uuid === val)
      this.form.cabinetName = RoomMode.cabinetName
      this.form.cabinetId = RoomMode.uuid
    },

    /** 查询设备模板管理列表 */
    getModelList() {
      this.loading = true;
      listRmEquipmentModels().then(response => {
        this.RmEquipmentModelList = response.rows;
        // console.log("this.RmEquipmentModelList===", this.RmEquipmentModelList);
        this.loading = false;
      });
    },
    selectModel(val) {
      const res = this.RmEquipmentModelList.find(item => item.uuid === val)
      getRmEquipmentModel(val).then(response => {
        // this.form = response.data;
        this.rmEquipmentSlotList = response.data.rmEquipmentSlotModelList;
        this.form.model = response.data.model
        this.form.equipmentType = response.data.equipmentType
        this.form.brand = response.data.brand
        this.form.manufacturer = response.data.manufacturer
        this.form.occupiedU = response.data.occupiedU
        this.form.length = response.data.length
        this.form.width = response.data.width
        this.form.height = response.data.height
        this.form.modelId = response.data.uuid
        this.form.modelName = response.data.equipmentName
      });

    },

  }
};
</script>
<style lang="scss" scoped>
.content {
  min-height: 70vh;
}
</style>
