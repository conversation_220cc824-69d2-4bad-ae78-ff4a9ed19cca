<template>
  <div class="abc">
    <div ref="threeContainer"></div>
    <div class="card" ref="cardId" type="module">
      <h2 v-if="selectedCabinet">机柜名称: {{ selectedCabinet.name }}</h2>
      <p v-if="selectedCabinet">位置: {{ selectedCabinet.location }}</p>
      <p v-if="selectedCabinet">容量: {{ selectedCabinet.capacity }}</p>
      <p v-if="selectedCabinet">描述: {{ selectedCabinet.description }}</p>
    </div>
  </div>
</template>

<script>
import { index,getjigui } from './shiyan/index3.js'; // 确保路径正确
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
import { GUI } from 'three/examples/jsm/libs/lil-gui.module.min.js';
import { Stats } from 'three/examples/jsm/libs/stats.module.js';
import { Line2 } from 'three/examples/jsm/lines/Line2.js';
import { LineGeometry } from 'three/examples/jsm/lines/LineGeometry.js';
import { LineMaterial } from 'three/examples/jsm/lines/LineMaterial.js';
import { FBXLoader } from 'three/examples/jsm/loaders/FBXLoader.js';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js';
import { EffectComposer } from 'three/examples/jsm/postprocessing/EffectComposer.js';
import { OutlinePass } from 'three/examples/jsm/postprocessing/OutlinePass.js';
import { RenderPass } from 'three/examples/jsm/postprocessing/RenderPass.js';
import { ShaderPass } from 'three/examples/jsm/postprocessing/ShaderPass.js';
import { GammaCorrectionShader } from 'three/examples/jsm/shaders/GammaCorrectionShader.js';
import { listRmCabinet, getRmCabinet, delRmCabinet, addRmCabinet, updateRmCabinet } from "@/api/cabinet/RmCabinet";

export default {
  name: 'index',
  data() {
    return {
      isThreeContainer2Visible: true, // 初始状态为可见
      selectedCabinet: null, // 用于存储选中的机柜信息
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        deptId: null,
        status: null,
        remarks: null,
        cabinetName: null,
        cabinetCode: null,
        roomId: null,
        roomName: null,
        totalU: null,
        manufacturer: null,
        cabinetType: null,
        length: null,
        width: null,
        height: null,
        positionX: null,
        positionY: null,
        orientation: null,
        modelId: null,
        modelName: null
      },
        // 机柜模板管理表格数据
        RmCabinetModelList: [],
    };
  },
  mounted() {
    this.initThree();
    this.initCard();
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      this.loading = true;
      listRmCabinet(this.queryParams).then(response => {
        this.RmCabinetModelList = response.rows;
        console.log("this.RmCabinetModelList==",this.RmCabinetModelList);
        this.total = response.total;
        this.loading = false;
        // getjigui(this.RmCabinetModelList);
      });      
    },
    initThree() {
      // 初始化 Three.js 场景和渲染器
    },
    initCard() {
      const compDiv = this.$refs.cardId;
      const threeContainer = this.$refs.threeContainer;
      if (compDiv) {
        index(compDiv, this.updateSelectedCabinet,threeContainer,this.RmCabinetModelList); // 将 compDiv 和回调函数传递给 index3.js
        console.log('compDiv111', compDiv);
      } else {
        console.error('compDiv is undefined in ThreeScene.vue');
      }
    },
    updateSelectedCabinet(cabinetData) {
      this.selectedCabinet = cabinetData;
      console.log('Selected Cabinet:', this.selectedCabinet);
    }
  }
};
</script>

<style scoped>
/* 样式可以根据需要调整 */
.card {
  /* border: red 5px solid; */
  height: 700px;
  width: 400px;
  position: absolute;
  display: inline-block;
  color: white;
  background: rgb(65, 92, 244);
  opacity: 0; /* 初始透明度为0 */
  transition: opacity 1.5s ease-in-out; /* 定义过渡效果 */
}
/* .abc {
  padding: 0px;
  margin: 0px;
  width: 100%;
} */
 /* .abc{ */
  /* border: red 5px solid; */
 /* } */
/* .lvse {
  height: 100vh; 
  width: 100%;
  border: 1px solid red;
  transition: width 0.3s ease; /* 添加过渡效果 */
/* } */ 
</style>