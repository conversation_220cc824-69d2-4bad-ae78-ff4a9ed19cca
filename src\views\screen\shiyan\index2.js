/* eslint-disable no-unused-vars */

import * as THREE from 'three';
import Stats from 'three/examples/jsm/libs/stats.module.js';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
console.log("OrbitControls",OrbitControls)
import { GUI } from 'three/examples/jsm/libs/lil-gui.module.min.js';
import mesh from './point.js';
import lou from './lou.js';
import earth from './earth.js';
import cizhuan from './cizhuan.js';
/* eslint-disable no-unused-vars */

const geometry = new THREE.PlaneGeometry(500,10);
const TextureLoader = new THREE.TextureLoader();
const texture = TextureLoader.load('./pic/zhuanwan.png');
texture.offset.x=0.5;
texture.wrapS = THREE.RepeatWrapping;
const material = new THREE.MeshBasicMaterial({
    map: texture,
    transparent: true,
});
const plane = new THREE.Mesh(geometry, material);
texture.repeat.x=50;
plane.rotateX(-Math.PI / 2);

const scene = new THREE.Scene();
// scene.add(cizhuan);
scene.add(plane);
const axesHelper = new THREE.AxesHelper( 150 );
const gridHelper = new THREE.GridHelper( 300, 50 , 0x004444, 0x004444);
gridHelper.position.y=-1;
const ligth = new THREE.DirectionalLight(0xffffff,0.5);
ligth.position.set( 5, 10, 5 );
ligth.target = plane;
scene.add(ligth,axesHelper,gridHelper);

const camera = new THREE.PerspectiveCamera( 75, window.innerWidth / window.innerHeight, 0.1, 1000 );
camera.position.set( 10, 10, 10 );
camera.lookAt(0,0,0);
scene.add( camera );

const renderer = new THREE.WebGLRenderer({
    alpha: false
});
renderer.setSize( window.innerWidth, window.innerHeight );
// document.getElementById('cardId').appendChild(renderer.domElement);
// document.body.appendChild(renderer.domElement);
let isRendering = true; 
// document.getElementById('red').addEventListener('click',function(){
//     isRendering = !isRendering;
// });
const controls = new OrbitControls( camera, renderer.domElement );
controls.addEventListener('change', function () {
    renderer.render(scene, camera); //执行渲染操作
});

function render(){
    if(isRendering)
    texture.offset.x += 0.01;
    renderer.render(scene, camera);
    requestAnimationFrame(render);
    
}
render();


// function render() {
//     earth.rotateY(0.001);
//     renderer.render(scene, camera);
//     requestAnimationFrame(render);
// }
// render();
export default renderer.domElement;
