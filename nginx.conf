
#user  nobody;
worker_processes  1;

#error_log  logs/error.log;
#error_log  logs/error.log  notice;
#error_log  logs/error.log  info;

#pid        logs/nginx.pid;


events {
    worker_connections  1024;
}


http {
    include       mime.types;
    default_type  application/octet-stream;

    #log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
    #                  '$status $body_bytes_sent "$http_referer" '
    #                  '"$http_user_agent" "$http_x_forwarded_for"';

    #access_log  logs/access.log  main;

    sendfile        on;
    #tcp_nopush     on;

    #keepalive_timeout  0;
    keepalive_timeout  65;

    server_tokens off; # 404页面显示Nginx的当前版本号
    client_max_body_size 50m; # 大小限制
    #gzip  on;

    server {
        listen       9090; # 监管平台前端
        server_name  localhost www.jyjiandu.com.cn;

        server_tokens off; # 404页面显示Nginx的当前版本号

        #charset koi8-r;

        #access_log  logs/host.access.log  main;

        location /prod-api/ {
            proxy_pass http://localhost:59015/;
            proxy_set_header Host $http_host; #后台可以获取到完整的ip+端口号
            proxy_set_header X-Real-IP $remote_addr; #后台可以获取到用户访问的真实ip地址
        }

        location / {
            root   html/supervise-dist;
            index  index.html index.htm;
            try_files $uri $uri/ /index.html;
            # 错误页面
            error_page 400 404 413 502 504 /index.html;
            access_log  logs/hidden.access.log;
            error_log  logs/hidden.error.log error; #记录一下error日志
        }

        location /profile/ {
            alias /home/<USER>/uploadPath; #文件访问地址
            autoindex off;
            index  index.html index.htm;
        }

        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   html;
        }

    }

    server {
        listen       9092; # 监管平台手机端
        server_name  localhost www.jyjiandu.com.cn;

        server_tokens off; # 404页面显示Nginx的当前版本号

        #charset koi8-r;

        #access_log  logs/host.access.log  main;

        location /prod-api/ {
            proxy_pass http://localhost:59015/;
            proxy_set_header Host $http_host; #后台可以获取到完整的ip+端口号 
            proxy_set_header X-Real-IP $remote_addr; #后台可以获取到用户访问的真实ip地址 
        }

        location / {
            root   html/supervise-h5;
            index  index.html index.htm;
            try_files $uri $uri/ /index.html;
            # 错误页面        
            error_page 400 404 413 502 504 /index.html;
            access_log  logs/hidden.access.log;
            error_log  logs/hidden.error.log error; #记录一下error日志
        }

        location /profile/ {
            alias /home/<USER>/uploadPath; #文件访问地址    
            autoindex off;
            index  index.html index.htm;
        }

        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   html;
        }

    }

    server {
        listen       9096 ssl; # 智慧e社区管理端
        server_name  localhost echuxing.com.cn www.echuxing.com.cn;

        server_tokens off; # 404页面显示Nginx的当前版本号

        #ssl证书的pem文件路径
        ssl_certificate  /usr/local/nginx/ssl_nginx/echuxing/www.echuxing.com.cn.pem;
        #ssl证书的key文件路径
        ssl_certificate_key /usr/local/nginx/ssl_nginx/echuxing/www.echuxing.com.cn.key;

        #charset koi8-r;

        #access_log  logs/host.access.log  main;

        location /prod-api/ {
            proxy_pass http://localhost:60001/;
            proxy_set_header Host $http_host; #后台可以获取到完整的ip+端口号
            proxy_set_header X-Real-IP $remote_addr; #后台可以获取到用户访问的真实ip地址
        }

        location / {
            root   html/community-dist;
            index  index.html index.htm;
            try_files $uri $uri/ /index.html;
            # 错误页面
            error_page 400 404 413 502 504 /index.html;
            access_log  logs/hidden.access.log;
            error_log  logs/hidden.error.log error; #记录一下error日志
        }

        location /profile/ {
            alias /home/<USER>/uploadPath; #文件访问地址
            autoindex off;
            index  index.html index.htm;
        }

        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   html;
        }

    }

    server {
        listen       9098; # 人力招聘系统前端
        server_name  localhost;

        server_tokens off; # 404页面显示Nginx的当前版本号

        #charset koi8-r;

        #access_log  logs/host.access.log  main;

        location /prod-api/ {
            proxy_pass http://localhost:61002/;
            proxy_set_header Host $http_host; #后台可以获取到完整的ip+端口号
            proxy_set_header X-Real-IP $remote_addr; #后台可以获取到用户访问的真实ip地址
        }

        location / {
            root   html/recruit-dist;
            index  index.html index.htm;
            try_files $uri $uri/ /index.html;
            # 错误页面
            error_page 400 404 413 502 504 /index.html;
            access_log  logs/hidden.access.log;
            error_log  logs/hidden.error.log error; #记录一下error日志
        }

        location /profile/ {
            alias /home/<USER>/uploadPath; #文件访问地址
            autoindex off;
            index  index.html index.htm;
        }

        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   html;
        }

    }

    server {
        listen       9100; # 人力招聘平台手机端
        server_name  localhost;

        server_tokens off; # 404页面显示Nginx的当前版本号

        #charset koi8-r;

        #access_log  logs/host.access.log  main;

        location /prod-api/ {
            proxy_pass http://localhost:61002/;
            proxy_set_header Host $http_host; #后台可以获取到完整的ip+端口号
            proxy_set_header X-Real-IP $remote_addr; #后台可以获取到用户访问的真实ip地址
        }

        location / {
            root   html/recruit-h5;
            index  index.html index.htm;
            try_files $uri $uri/ /index.html;
            # 错误页面
            error_page 400 404 413 502 504 /index.html;
            access_log  logs/hidden.access.log;
            error_log  logs/hidden.error.log error; #记录一下error日志
        }

        location /profile/ {
            alias /home/<USER>/uploadPath; #文件访问地址
            autoindex off;
            index  index.html index.htm;
        }

        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   html;
        }

    }

    # another virtual host using mix of IP-, name-, and port-based configuration
    #
    #server {
    #    listen       8000;
    #    listen       somename:8080;
    #    server_name  somename  alias  another.alias;

    #    location / {
    #        root   html;
    #        index  index.html index.htm;
    #    }
    #}


    # HTTPS server
    #
    #server {
    #    listen       443 ssl;
    #    server_name  localhost;

    #    ssl_certificate      cert.pem;
    #    ssl_certificate_key  cert.key;

    #    ssl_session_cache    shared:SSL:1m;
    #    ssl_session_timeout  5m;

    #    ssl_ciphers  HIGH:!aNULL:!MD5;
    #    ssl_prefer_server_ciphers  on;

    #    location / {
    #        root   html;
    #        index  index.html index.htm;
    #    }
    #}

}
