<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import resize from './mixins/resize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '350px'
    },
    autoResize: {
      type: Boolean,
      default: true
    },
    chartData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        this.setOptions(val)
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons')
      this.setOptions(this.chartData)
    },
    setOptions({ moneyData, ymoneyData,monthData,titlename } = {}) {
      this.chart.setOption({
        xAxis: {
          // type: 'category',
          data: monthData,
          // boundaryGap: false,
          axisTick: {
            show: false
          }
        },
        grid: {
          left: 10,
          right: 10,
          bottom: 20,
          top: 30,
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          },
          padding: [5, 10]
        },
        yAxis: {
          axisTick: {
            show: false
          }
        },
        legend: {
          data: titlename
        },
        series: [{
          name: titlename[0], itemStyle: {
            normal: {
              color: '#3888fa',
              lineStyle: {
                color: '#3888fa',
                width: 2
              }
            }
          },
          smooth: true,
          type: 'bar',
          data: moneyData,
          label: {
            show: true,
            position: 'top',
          },
          animationDuration: 2800,
          animationEasing: 'cubicInOut',
          barWidth:'30%'
        },
        {
          name: titlename[1],
          smooth: true,
          type: 'bar',
          itemStyle: {
            normal: {
              color: '	#9400D3',
              lineStyle: {
                color: '#FFD700',
                width: 2
              },
              areaStyle: {
                color: '#f3f8ff'
              }
            }
          },
          data: ymoneyData,
             label: {
            show: true,
            position: 'top'
          },
          animationDuration: 2800,
          animationEasing: 'quadraticOut',
           barWidth:'30%'
        }]
      })
    }
  }
}
</script>
