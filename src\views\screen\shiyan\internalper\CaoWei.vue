<template>
  <div class="caowei-container" v-if="visible" @click.stop>
    <div class="caowei-content">
      <div class="caowei-header">
        <h3>板卡信息</h3>
        <div class="close-btn" @click="closePanel">×</div>
      </div>
      <div class="caowei-body">
        <div class="caowei-layout">
          <!-- 基本信息部分 -->
          <div class="info-section">
            <h4>基本信息</h4>
            <el-form ref="form" :model="slotData" label-width="120px">
              <el-form-item class="label" label="板卡编号：" prop="slotNumber" v-if="slotData">
                <span class="value">{{ slotData.slotNumber || '未知' }}</span>
              </el-form-item>
              <el-form-item class="label" label="X坐标：" prop="positionX" v-if="slotData">
                <span class="value">{{ slotData.positionX || '未知' }}</span>
              </el-form-item>
              <el-form-item class="label" label="Y坐标：" prop="positionY" v-if="slotData">
                <span class="value">{{ slotData.positionY || '未知' }}</span>
              </el-form-item>
              <el-form-item class="label" label="宽度：" prop="width" v-if="slotData">
                <span class="value">{{ slotData.width || '未知' }}</span>
              </el-form-item>
              <el-form-item class="label" label="高度：" prop="height" v-if="slotData">
                <span class="value">{{ slotData.height || '未知' }}</span>
              </el-form-item>
              <el-form-item class="label" label="状态：" prop="status" v-if="slotData">
                <span class="value" :class="{'status-normal': slotData.status === '1'}">
                  {{ slotData.status === '0' ? '未使用' : '已使用' }}
                </span>
              </el-form-item>
              <el-form-item class="label" label="备注：" prop="remarks" v-if="slotData && slotData.remarks">
                <span class="value">{{ slotData.remarks || '无' }}</span>
              </el-form-item>
              <el-form-item class="label" label="板卡ID：" prop="boardId" v-if="slotData && slotData.boardId">
                <span class="value">{{ slotData.boardId || '无' }}</span>
              </el-form-item>
            </el-form>
          </div>

          <!-- 板卡图形展示部分 -->
          <div class="board-display-section">
            <h4>板卡图形 - {{ slotData && slotData.rmEquipmentBoardList ? slotData.rmEquipmentBoardList.length : 0 }}块</h4>

            <!-- 加载状态指示器 -->
            <div v-if="loading" class="loading-container">
              <div class="loading-spinner"></div>
              <div class="loading-text">正在加载板卡数据...</div>
            </div>

            <!-- 板卡显示区域 - 仅在非加载状态下显示 -->
            <div v-else class="banka-display">
              <!-- 板卡和端口信息布局 -->
              <div class="board-port-layout">
                <!-- 左侧板卡区域 -->
                <div class="board-area">
                  <!-- 板卡列表 - 使用16列固定布局 -->
                  <div class="board-container">
                    <!-- 遍历1-16列 -->
                    <div
                      v-for="colIndex in 1"
                      :key="'col-'+colIndex"
                      class="board-column"
                    >
                      <!-- 列标签 -->
                      <div class="column-label">{{ colIndex }}</div>

                      <!-- 创建16个位置的板卡容器 -->
                      <div class="board-positions">
                        <!-- 如果该列没有板卡，显示一个空区域 -->
                        <div
                          v-if="!getBoardsInColumn(colIndex).length"
                          class="empty-column"
                        >
                          <div class="empty-label">空</div>
                        </div>

                        <!-- 如果该列有板卡 -->
                        <template v-else>
                          <!-- 如果该列只有一个板卡，占据整列显示 -->
                          <div
                            v-if="hasOnlyOneBoard(colIndex)"
                            class="banka-card"
                            :class="{'active-card': getBoardsInColumn(colIndex)[0].status === '1'}"
                            @mouseover="(event) => showBoardInfo(getBoardsInColumn(colIndex)[0], event)"
                            @mouseleave="hideBoardInfo"
                          >
                            <div class="banka-image">
                              <div class="banka-number">{{ getBoardsInColumn(colIndex)[0].boardNumber || colIndex }}</div>

                              <!-- 端口区域 -->
                              <div class="ports-container">
                                <!-- 左侧端口 -->
                                <div class="ports ports-left" v-if="getBoardPorts(getBoardsInColumn(colIndex)[0]).length > 0">
                                  <div
                                    v-for="(port, portIndex) in getLeftPorts(getBoardsInColumn(colIndex)[0])"
                                    :key="'left-port-' + portIndex"
                                    class="port"
                                    :class="{'port-active': port.status === '0'}"
                                    @click="showPortInfo(port)"
                                    @mouseover="(event) => showPortTooltip(port, event)"
                                    @mouseleave="hidePortTooltip"
                                  >
                                    <span class="port-number">{{ port.portNumber }}</span>
                                  </div>
                                </div>

                                <!-- 右侧端口 -->
                                <div class="ports ports-right" v-if="getBoardPorts(getBoardsInColumn(colIndex)[0]).length > 0">
                                  <div
                                    v-for="(port, portIndex) in getRightPorts(getBoardsInColumn(colIndex)[0])"
                                    :key="'right-port-' + portIndex"
                                    class="port"
                                    :class="{'port-active': port.status === '0'}"
                                    @click="showPortInfo(port)"
                                    @mouseover="(event) => showPortTooltip(port, event)"
                                    @mouseleave="hidePortTooltip"
                                  >
                                    <span class="port-number">{{ port.portNumber }}</span>
                                  </div>
                                </div>
                              </div>
                            </div>
                            <div class="banka-info">
                              <div class="banka-name">{{ getBoardsInColumn(colIndex)[0].boardName || '板卡 ' + colIndex }}</div>
                              <div class="banka-status" :class="{'status-active': getBoardsInColumn(colIndex)[0].status === '0'}">
                                {{ getBoardsInColumn(colIndex)[0].status === '1' ? '停用' : '正常' }}
                              </div>
                            </div>
                          </div>

                          <!-- 如果该列有多个板卡，根据数量平均分配高度 -->
                          <template v-else>
                            <div
                              v-for="(board, index) in getBoardsInColumn(colIndex)"
                              :key="board.uuid"
                              class="banka-card"
                              :class="{'active-card': board.status === '1'}"
                              :style="getBoardPositionStyle(board, colIndex)"
                              @mouseover="(event) => showBoardInfo(board, event)"
                              @mouseleave="hideBoardInfo"
                            >
                              <div class="banka-image">
                                <div class="banka-number">{{ board.boardNumber || colIndex + '-' + (index + 1) }}</div>

                                <!-- 端口区域 -->
                                <div class="ports-container">
                                  <!-- 左侧端口 -->
                                  <div class="ports ports-left" v-if="getBoardPorts(board).length > 0">
                                    <div
                                      v-for="(port, portIndex) in getLeftPorts(board)"
                                      :key="'left-port-' + portIndex"
                                      class="port"
                                      :class="{'port-active': port.status === '0'}"
                                      @click="showPortInfo(port)"
                                      @mouseover="(event) => showPortTooltip(port, event)"
                                      @mouseleave="hidePortTooltip"
                                    >
                                      <span class="port-number">{{ port.portNumber }}</span>
                                    </div>
                                  </div>

                                  <!-- 右侧端口 -->
                                  <div class="ports ports-right" v-if="getBoardPorts(board).length > 0">
                                    <div
                                      v-for="(port, portIndex) in getRightPorts(board)"
                                      :key="'right-port-' + portIndex"
                                      class="port"
                                      :class="{'port-active': port.status === '0'}"
                                      @click="showPortInfo(port)"
                                      @mouseover="(event) => showPortTooltip(port, event)"
                                      @mouseleave="hidePortTooltip"
                                    >
                                      <span class="port-number">{{ port.portNumber }}</span>
                                    </div>
                                  </div>
                                </div>
                              </div>
                              <div class="banka-info">
                                <div class="banka-name">{{ board.boardName || '板卡 ' + (index + 1) }}</div>
                                <div class="banka-status" :class="{'status-active': board.status === '0'}">
                                  {{ board.status === '1' ? '停用' : '正常' }}
                                </div>
                              </div>
                            </div>
                          </template>
                        </template>
                      </div>
                    </div>
                  </div>

                  <!-- 如果没有板卡数据，显示提示信息 -->
                  <div v-if="!slotData" class="no-data">
                    <span>暂无板卡数据</span>
                  </div>
                </div>

                <!-- 右侧端口信息面板 -->
                <div class="port-info-panel">
                  <DuanKou />
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 板卡信息悬浮框 -->
        <div class="board-tooltip" v-if="activeBoard && !activePort" :style="tooltipStyle">
          <div class="tooltip-header">
            <span class="tooltip-title">{{ activeBoard.boardName || activeBoard.boardNumber }}板卡</span>
            <span class="tooltip-status" :class="{'status-active': activeBoard.status === '0'}">
              {{ activeBoard.status === '1' ? '停用' : '正常' }}
            </span>
          </div>
          <div class="tooltip-content">
            <div class="tooltip-item">
              <span class="tooltip-label">位置:</span>
              <span class="tooltip-value">X: {{ activeBoard.positionX || 0 }}, Y: {{ activeBoard.positionY || 0 }}</span>
            </div>
            <div class="tooltip-item">
              <span class="tooltip-label">类型:</span>
              <span class="tooltip-value">{{ activeBoard.boardType || '未知' }}</span>
            </div>
            <div class="tooltip-item">
              <span class="tooltip-label">型号:</span>
              <span class="tooltip-value">{{ activeBoard.model || '未知' }}</span>
            </div>
            <div class="tooltip-item" v-if="activeBoard.remarks">
              <span class="tooltip-label">备注:</span>
              <span class="tooltip-value">{{ activeBoard.remarks }}</span>
            </div>
          </div>
        </div>

        <!-- 端口信息悬浮框 -->
        <div class="port-tooltip" v-if="activePort" :style="tooltipStyle">
          <div class="tooltip-header">
            <span class="tooltip-title">{{ activePort.portNumber }}端口</span>
            <span class="tooltip-status" :class="{'status-active': activePort.status === '0'}">
              {{ activePort.status === '1' ? '停用' : '正常' }}
            </span>
          </div>
          <div class="tooltip-content">
            <div class="tooltip-item">
              <span class="tooltip-label">端口类型:</span>
              <span class="tooltip-value">{{ activePort.portType || '未知' }}</span>
            </div>
            <div class="tooltip-item">
              <span class="tooltip-label">位置:</span>
              <span class="tooltip-value">X: {{ activePort.positionX || 0 }}, Y: {{ activePort.positionY || 0 }}</span>
            </div>
            <div class="tooltip-item" v-if="activePort.remarks">
              <span class="tooltip-label">备注:</span>
              <span class="tooltip-value">{{ activePort.remarks }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { EventBus } from '@/utils/eventBus';
import { listRmEquipmentBoard, getRmEquipmentBoard } from "@/api/equipment/RmEquipmentBoard";
import DuanKou from './DuanKou.vue';

export default {
  name: 'CaoWei',
  components: { DuanKou },
  data() {
    return {
      visible: false,
      slotData: null,
      activeBoard: null, // 当前激活的板卡
      activePort: null, // 当前激活的端口
      tooltipPosition: { x: 0, y: 0 }, // 提示框位置
      mousePosition: { x: 0, y: 0 }, // 鼠标位置
      loading: false, // 加载状态
      boardPortsMap: {}, // 存储板卡ID与其端口数据的映射
    };
  },
  computed: {
    tooltipStyle() {
      // 根据鼠标位置计算提示框样式
      return {
        left: (this.mousePosition.x + 10) + 'px',
        top: (this.mousePosition.y - 10) + 'px'
      };
    }
  },
  mounted() {
    this.initEventListeners();
    // 添加鼠标移动事件监听
    document.addEventListener('mousemove', this.handleMouseMove);
  },
  methods: {
    initEventListeners() {
      // 监听板卡点击事件
      EventBus.$on('slot-clicked', (data) => {
        // 打印接收到的板卡数据，便于调试
        console.log('接收到点击板卡数据:', data);

        // 立即设置加载状态
        this.loading = true;

        // 显示面板
        this.visible = true;

        // 设置板卡数据
        this.slotData = data;

        // 确保rmEquipmentBoardList已初始化为空数组
        if (this.slotData && !this.slotData.rmEquipmentBoardList) {
          this.slotData.rmEquipmentBoardList = [];
        }

        // 如果有板卡数据且有uuid，则通过API获取详细数据
        if (data && (data.uuid || data.slotId)) {
          // 确保设备名称和机房ID可用
          // 使用uuid或slotId作为参数
          const slotId = data.uuid || data.slotId;

          // 延迟一点时间再获取数据，确保UI有足够时间显示加载状态
          setTimeout(() => {
            this.fetchSlotData(slotId);
          }, 100);
        } else {
          // 如果没有有效的ID，则关闭加载状态
          console.warn('没有有效的板卡ID，无法获取板卡数据');
          this.loading = false;
        }
      });

      // 监听板卡清除事件
      EventBus.$on('slot-cleared', () => {
        this.visible = false;
        this.loading = false;
        this.slotData = null;
      });
    },

    // 获取板卡详细数据
    fetchSlotData(uuid) {
      // 先清空现有的板卡数据，避免显示旧数据
      if (this.slotData) {
        this.slotData.rmEquipmentBoardList = [];
      }

      // 清空板卡端口映射
      this.boardPortsMap = {};

      // 设置加载状态
      this.loading = true;

      // 准备查询参数
      const queryParams = {
        pageNum: 1,
        pageSize: 100, // 增加页面大小，确保获取所有板卡
        slotId: uuid // 使用传入的板卡id
      };

      // 从点击的板卡获取真实的参数数据
      // 添加交换机id（roomId）到查询参数中
      if (this.slotData && this.slotData.roomId) {
        queryParams.roomId = this.slotData.roomId;
        // console.log('使用交换机id:', this.slotData.roomId);
      }

      // 添加交换机名称（equipmentName）到查询参数中
      if (this.slotData && this.slotData.equipmentName) {
        queryParams.equipmentName = this.slotData.equipmentName;
        // console.log('使用交换机名称:', this.slotData.equipmentName);
      }

      // 使用listRmEquipmentBoard方法获取板卡数据
      listRmEquipmentBoard(queryParams).then(response => {
        // console.log('获取到板卡列表:', response.rows);

        // 确保slotData存在
        if (!this.slotData) {
          console.error('slotData不存在，无法更新板卡列表');
          return;
        }

        // 更新板卡数据，添加板卡列表
        if (response.rows && response.rows.length > 0) {
          // 延迟一点时间再更新数据，确保UI有足够时间显示加载状态
          setTimeout(() => {
            this.slotData.rmEquipmentBoardList = response.rows;

            // 初始化板卡数据结构
            this.initEmptyBoardsData();

            // 获取每个板卡的端口数据
            this.fetchBoardPortsData();

            // 关闭加载状态
            this.loading = false;
          }, 500);
        } else {
          // 如果没有板卡数据，设置为空数组
          setTimeout(() => {
            this.slotData.rmEquipmentBoardList = [];
            console.log('未找到相关板卡数据');

            // 初始化板卡数据结构
            this.initEmptyBoardsData();

            // 关闭加载状态
            this.loading = false;
          }, 500);
        }
      }).catch(error => {
        console.error('获取板卡列表失败:', error);

        // 延迟一点时间再更新状态，确保UI有足够时间显示加载状态
        setTimeout(() => {
          if (this.slotData) {
            this.slotData.rmEquipmentBoardList = [];
            // 即使获取失败，也初始化空板卡数据结构
            this.initEmptyBoardsData();
          }

          // 关闭加载状态
          this.loading = false;
        }, 500);
      });
    },

    // 获取所有板卡的端口数据
    fetchBoardPortsData() {
      if (!this.slotData || !this.slotData.rmEquipmentBoardList || this.slotData.rmEquipmentBoardList.length === 0) {
        return;
      }

      // 遍历所有板卡，获取每个板卡的详细信息（包括端口）
      this.slotData.rmEquipmentBoardList.forEach(board => {
        if (board.uuid) {
          this.fetchBoardDetail(board.uuid);
        }
      });
    },

    // 获取单个板卡的详细信息
    fetchBoardDetail(boardId) {
      getRmEquipmentBoard(boardId).then(response => {
        if (response.data) {
          console.log('获取到板卡详细信息:', response.data);

          // 存储板卡的端口数据
          if (response.data.rmEquipmentPortList) {
            this.$set(this.boardPortsMap, boardId, response.data.rmEquipmentPortList);
          } else {
            this.$set(this.boardPortsMap, boardId, []);
          }
        }
      }).catch(error => {
        console.error('获取板卡详细信息失败:', error);
        this.$set(this.boardPortsMap, boardId, []);
      });
    },

    // 初始化板卡数据结构
    initEmptyBoardsData() {
      // 确保rmEquipmentBoardList已初始化
      if (!this.slotData.rmEquipmentBoardList) {
        this.slotData.rmEquipmentBoardList = [];
      }
    },

    // 关闭面板
    closePanel() {
      this.visible = false;
      // 发送清除事件
      EventBus.$emit('slot-cleared');
    },

    // 显示板卡信息
    showBoardInfo(board, event) {
      this.activeBoard = board;
      // if (event) {
      //   this.mousePosition = {
      //     x: event.clientX,
      //     y: event.clientY
      //   };
      // }
    },

    // 隐藏板卡信息
    hideBoardInfo() {
      this.activeBoard = null;
      this.activePort = null;
    },

    // 监听鼠标移动
    handleMouseMove(event) {
      if (this.activeBoard || this.activePort) {
        this.mousePosition = {
          x: event.clientX,
          y: event.clientY
        };
      }
    },

    // 获取指定列的所有板卡，并按Y坐标排序
    getBoardsInColumn(colIndex) {
      if (!this.slotData || !this.slotData.rmEquipmentBoardList) {
        return [];
      }

      // 获取所有板卡
      const allBoards = this.slotData.rmEquipmentBoardList;

      // 根据positionX值筛选板卡
      const filteredBoards = allBoards.filter(board => {
        // 优先使用positionX值（如果存在）
        if (board.positionX !== undefined && board.positionX !== null) {
          // X值就是列号
          return parseInt(board.positionX) === colIndex;
        }

        // 如果没有positionX，尝试从boardNumber中提取X值
        const boardNumberParts = String(board.boardNumber || '').split('-');
        if (boardNumberParts && boardNumberParts.length > 0) {
          const boardCol = parseInt(boardNumberParts[0]);
          return !isNaN(boardCol) && boardCol === colIndex;
        }

        return false; // 如果无法确定X值，则不包含该板卡
      });

      // 为每个板卡提取Y坐标
      const boardsWithY = filteredBoards.map(board => {
        // 获取Y坐标，优先使用positionY
        let yPos = 1; // 默认值为1
        if (board.positionY !== undefined && board.positionY !== null) {
          yPos = parseInt(board.positionY);
        } else {
          // 尝试从boardNumber中提取Y值
          const boardNumberParts = String(board.boardNumber || '').split('-');
          if (boardNumberParts && boardNumberParts.length > 1) {
            const parsedY = parseInt(boardNumberParts[1]); // 假设boardNumber的第二部分表示Y坐标
            if (!isNaN(parsedY)) {
              yPos = parsedY;
            }
          }
        }

        return {
          ...board,
          yPosition: yPos // 添加一个yPosition属性用于排序
        };
      });

      // 按Y坐标从大到小排序（positionY大的在上方，小的在下方）
      return boardsWithY.sort((a, b) => b.yPosition - a.yPosition);
    },

    // 计算板卡的位置样式
    getBoardPositionStyle(board, colIndex) {
      // 获取该列中的所有板卡
      const boardsInColumn = this.getBoardsInColumn(colIndex);

      // 如果该列只有一个板卡，则占据整列
      if (boardsInColumn.length === 1) {
        return {
          width: '100%',
          height: '100%'
        };
      }

      // 如果有多个板卡，则根据板卡在列中的索引位置确定其位置
      // 找到当前板卡在列中的索引
      const boardIndex = boardsInColumn.findIndex(b => b.uuid === board.uuid);
      const heightPerBoard = 100 / boardsInColumn.length;

      // 由于我们现在是按Y坐标从大到小排序，所以索引0的板卡应该在最上方
      // 索引越大的板卡应该越靠下方
      return {
        width: '100%',
        height: `${heightPerBoard}%`,
        position: 'absolute',
        top: `${boardIndex * heightPerBoard}%`
      };
    },

    // 判断该列是否只有一个板卡
    hasOnlyOneBoard(colIndex) {
      const boards = this.getBoardsInColumn(colIndex);
      return boards.length === 1;
    },

    // 获取板卡的所有端口
    getBoardPorts(board) {
      if (!board || !board.uuid || !this.boardPortsMap[board.uuid]) {
        return [];
      }
      return this.boardPortsMap[board.uuid];
    },

    // 获取板卡左侧的端口（偶数索引）
    getLeftPorts(board) {
      const ports = this.getBoardPorts(board);
      return ports.filter((_port, index) => index % 2 === 0);
    },

    // 获取板卡右侧的端口（奇数索引）
    getRightPorts(board) {
      const ports = this.getBoardPorts(board);
      return ports.filter((_port, index) => index % 2 === 1);
    },

    // 显示端口信息
    showPortInfo(port) {
      this.activePort = port;
      // 记录当前端口信息
      console.log('端口信息:', port);
      // 通过EventBus发送事件，显示端口详细信息面板
      EventBus.$emit('port-clicked', port);
    },

    // 显示端口悬浮提示
    showPortTooltip(port, event) {
      this.activePort = port;
      if (event) {
        this.mousePosition = {
          x: event.clientX,
          y: event.clientY
        };
      }
    },

    // 隐藏端口悬浮提示
    hidePortTooltip() {
      this.activePort = null;
    }
  },
  beforeDestroy() {
    // 移除事件监听器
    EventBus.$off('slot-clicked');
    EventBus.$off('slot-cleared');
    EventBus.$off('port-clicked');
    // 移除鼠标移动事件监听
    document.removeEventListener('mousemove', this.handleMouseMove);
  }
};
</script>

<style scoped>
.caowei-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
}

.caowei-content {
  width: 90%;
  max-width: 1800px;
  max-height: 95vh;
  background-color: rgba(51, 0, 102, 0.9);
  border-radius: 8px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative; /* 添加相对定位，使端口信息面板可以相对于它定位 */
  padding-right: 0; /* 为端口信息面板留出空间 */
}

.caowei-header {
  padding: 15px 20px;
  background-color: rgba(41, 0, 82, 0.9);
  display: flex;
  justify-content: space-between;
  align-items: center;
  /* border-bottom: 1px solid rgba(153, 102, 204, 0.3); */
}

.caowei-header h3 {
  margin: 0;
  color: #9966cc;
  font-size: 18px;
}

.close-btn {
  color: #9966cc;
  font-size: 24px;
  cursor: pointer;
  width: 30px;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  transition: all 0.3s;
}

.close-btn:hover {
  background-color: rgba(153, 102, 204, 0.2);
}

.caowei-body {
  padding: 20px;
  overflow: hidden;
  flex: 1;
}

.caowei-layout {
  display: flex;
  flex-direction: row;
  gap: 20px;
  height: 100%;
}

.info-section, .board-display-section {
  margin-bottom: 20px;
  background-color: rgba(31, 0, 62, 0.5);
  border-radius: 6px;
  padding: 15px;
  /* border: 1px solid rgba(153, 102, 204, 0.2); */
}

.info-section {
  flex: 0 0 300px;
  overflow-y: auto;
  overflow-x: auto;
  max-height: calc(100vh - 150px);
}

.board-display-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

h4 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #9966cc;
  font-size: 16px;
  border-bottom: 1px solid rgba(153, 102, 204, 0.2);
  padding-bottom: 8px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 15px;
}

.info-item {
  display: flex;
  flex-direction: column;
}

.label {
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  margin-bottom: 5px;
}

.value {
  color: #ffffff;
  font-size: 14px;
}

.status-normal {
  color: #52c41a; /* 正常状态为绿色 */
}

/* 板卡展示区域样式 */
.banka-display {
  width: 100%;
  height: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
}

/* 板卡容器样式 */
.board-container {
  width: 100%;
  background-color: #e6d9f2;
  border-radius: 4px;
  border: 1px solid #c8b3e6;
  padding: 10px;
  display: flex;
  flex-direction: row;
  gap: 1px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  height: 600px; /* 增加高度，使板卡显示更清晰 */
}

/* 列样式 */
.board-column {
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  border: 1px solid #c8b3e6;
  background-color: #f5f0fa;
}

/* 列标签样式 */
.column-label {
  position: absolute;
  top: 5px;
  left: 5px;
  font-size: 14px;
  color: #663399;
  font-weight: bold;
  z-index: 2;
  background-color: rgba(230, 217, 242, 0.8);
  padding: 3px 8px;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(153, 102, 204, 0.3);
}

/* 板卡位置容器 */
.board-positions {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden; /* 确保溢出内容被隐藏 */
  min-height: 200px; /* 确保容器有足够的高度显示板卡 */
}

/* 板卡卡片样式 */
.banka-card {
  width: 100%;
  height: 100%;
  border: 2px solid rgba(153, 102, 204, 0.5);
  border-radius: 5px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  backdrop-filter: blur(2px);
  background-color: #f0e6ff;
  margin-bottom: 2px;
  z-index: 1; /* 确保板卡在正常层级上 */
  min-height: 60px; /* 确保板卡有最小高度 */
  box-sizing: border-box; /* 确保边框不会增加元素的总宽高 */
}

.banka-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 15px rgba(153, 102, 204, 0.3);
  z-index: 5;
  border-color: rgba(153, 102, 204, 0.8);
}

/* 空列样式 */
.empty-column {
  width: 100%;
  height: 100%;
  background-color: #f9f6fc;
  background-image: repeating-linear-gradient(
    45deg,
    transparent,
    transparent 10px,
    rgba(200, 179, 230, 0.5) 10px,
    rgba(200, 179, 230, 0.5) 20px
  );
  position: relative;
}

.empty-label {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #9966cc;
  font-size: 18px;
  font-style: italic;
}

/* 活动板卡样式 */
.active-card {
  background-color: rgba(51, 0, 102, 0.7);
  border-color: rgba(102, 51, 153, 0.8);
  box-shadow: 0 0 12px rgba(102, 51, 153, 0.6);
}

.active-card:hover {
  background-color: rgba(51, 0, 102, 0.8);
  border-color: rgba(102, 51, 153, 1);
  box-shadow: 0 0 20px rgba(102, 51, 153, 0.8);
}

/* 板卡图片区域 */
.banka-image {
  flex: 1;
  background-color: rgba(31, 0, 62, 0.3);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  /* border-bottom: 1px solid rgba(204, 204, 204, 0.3); */
  padding: 10px 0;
}

/* 板卡编号 */
.banka-number {
  position: absolute;
  top: 5px;
  left: 5px;
  font-size: 12px;
  color: #ffffff;
  background-color: rgba(153, 102, 204, 0.7);
  padding: 2px 6px;
  border-radius: 10px;
  font-weight: bold;
}

/* 端口容器样式 */
.ports-container {
  display: flex;
  justify-content: space-between;
  width: 100%;
  padding: 0 5px;
  margin-top: 10px;
}

/* 左侧端口组 */
.ports {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* 端口样式 */
.port {
  width: 30px;
  height: 30px;
  background-color: #e0e0e0;
  border: 2px solid #5dee4a;
  border-radius: 4px;
  position: relative;
  cursor: pointer;
  transition: all 0.2s ease;
  margin: 5px 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.port:hover {
  transform: scale(1.15);
  box-shadow: 0 0 8px rgba(153, 102, 204, 0.8);
  border-color: #9966cc;
  z-index: 10;
}

.port-active {
  background-color: #bff5a4;
  border-color: #3f9c0c;
  box-shadow: 0 0 10px rgba(82, 196, 26, 0.5);
}

.port-number {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 12px;
  color: #333;
  font-weight: bold;
  text-shadow: 0 1px 1px rgba(255, 255, 255, 0.5);
}

/* 端口信息提示框 */
.port-tooltip {
  position: fixed;
  background-color: rgba(31, 0, 62, 0.95);
  border: 2px solid rgba(82, 196, 26, 0.7);
  border-radius: 8px;
  padding: 15px;
  min-width: 250px;
  max-width: 350px;
  z-index: 1100;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.7);
  pointer-events: none;
  backdrop-filter: blur(5px);
  transform: translateY(-10px);
  max-height: 400px;
  overflow-y: auto;
  overflow-x: auto;
}

/* 板卡信息区域 */
.banka-info {
  height: 50px;
  padding: 8px;
  background-color: rgba(31, 0, 62, 0.5);
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.banka-name {
  font-size: 12px;
  color: #ffffff;
  font-weight: bold;
  text-align: center;
}

.banka-status {
  font-size: 10px;
  padding: 2px 4px;
  border-radius: 10px;
  background-color: rgba(255, 85, 0, 0.3); /* 停用状态背景色 */
  color: #ff7733; /* 停用状态文字颜色 */
  text-align: center;
}

.status-active {
  background-color: rgba(82, 196, 26, 0.3); /* 正常状态背景色 */
  color: #6dff3e; /* 正常状态文字颜色 */
}

/* 无数据提示 */
.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px;
  color: rgba(255, 255, 255, 0.5);
  font-style: italic;
  font-size: 16px;
  width: 100%;
  height: 200px;
}

/* 加载状态容器 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 400px;
  color: #9966cc;
}

/* 加载旋转动画 */
.loading-spinner {
  width: 50px;
  height: 50px;
  border: 5px solid rgba(153, 102, 204, 0.3);
  border-radius: 50%;
  border-top-color: #9966cc;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* 加载文本 */
.loading-text {
  font-size: 16px;
  color: #9966cc;
  font-weight: bold;
}

/* 板卡和端口信息布局 */
.board-port-layout {
  display: flex;
  width: 100%;
  height: 100%;
}

/* 左侧板卡区域 */
.board-area {
  flex: 1;
  overflow: auto;
  margin-right: 10px;
}

/* 板卡容器在布局中的样式 */
.board-port-layout .board-container {
  width: 100%;
  height: 100%;
}

/* 端口信息面板 */
.port-info-panel {
  width: 300px;
  height: 100%;
  position: relative;
  overflow: hidden;
}

/* 提示框样式 */
.board-tooltip {
  position: fixed;
  background-color: rgba(31, 0, 62, 0.95);
  border: 2px solid rgba(153, 102, 204, 0.7);
  border-radius: 8px;
  padding: 15px;
  min-width: 250px;
  max-width: 350px;
  z-index: 1100;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.7);
  pointer-events: none;
  backdrop-filter: blur(5px);
  transform: translateY(-10px);
  max-height: 400px;
  overflow-y: auto;
  overflow-x: auto;
}

.tooltip-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  border-bottom: 2px solid rgba(153, 102, 204, 0.5);
  padding-bottom: 8px;
}

.tooltip-title {
  color: #ffffff;
  font-weight: bold;
  font-size: 16px;
  text-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
}

.tooltip-status {
  font-size: 14px;
  padding: 3px 8px;
  border-radius: 12px;
  background-color: rgba(255, 85, 0, 0.3); /* 停用状态背景色 */
  color: #ff7733; /* 停用状态文字颜色 */
  font-weight: bold;
}

.tooltip-content {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.tooltip-item {
  display: flex;
  flex-direction: column;
  background-color: rgba(41, 0, 82, 0.5);
  padding: 8px;
  border-radius: 6px;
  border-left: 3px solid rgba(153, 102, 204, 0.7);
}

.tooltip-label {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  margin-bottom: 4px;
  font-weight: bold;
}

.tooltip-value {
  color: #ffffff;
  font-size: 15px;
}
</style>