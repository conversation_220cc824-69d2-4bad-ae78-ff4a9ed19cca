import * as THREE from 'three';
console.log("three",THREE);
import Stats from 'three/examples/jsm/libs/stats.module.js';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
console.log("OrbitControls",OrbitControls)
import { GUI } from 'three/examples/jsm/libs/lil-gui.module.min.js';
import mesh from './point.js';
const obj = {
    a:0.01,
    color:0xffffff,
    isRotate:true,
    light :1,
};

const scene = new THREE.Scene();
// const geometry = new THREE.SphereGeometry( 1 );
// const material = new THREE.MeshPhongMaterial( { color: obj.color,
//         shininess:1000,
// } );
// const mesh = new THREE.Mesh( geometry, material );
scene.add(mesh);

// mesh.position.set( 0, 0, 0 );
const axesHelper = new THREE.AxesHelper(150);
scene.add( mesh,axesHelper );
const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
directionalLight.position.set( 5, 10, 5 );
directionalLight.target=mesh;
const dirLightHelper = new THREE.DirectionalLightHelper(directionalLight, 5);
scene.add(directionalLight, dirLightHelper);

const camera = new THREE.PerspectiveCamera( 75, window.innerWidth / window.innerHeight, 0.1, 1000 );
camera.position.set( 5, 3, 5 );
camera.lookAt(mesh.position);

const gui = new GUI();
const tiaokong = gui.addFolder("调控");
tiaokong.add(mesh.position,'x',1,10).name("X坐标").step(0.1);
tiaokong.add(obj,'a',0.01,1).name("转动速度").onChange(function(value){
    mesh.rotateY(value);
});
tiaokong.addColor(obj,'color').name("颜色").onChange(function(value){
    mesh.material.color.set(value);
});
tiaokong.add(obj,'y',{
    min:0,
    max:10,
    center:5,
}).name("Y坐标").onChange(function(value){
    mesh.position.x=value;
});
tiaokong.add(obj,'isRotate').name("是否旋转").onChange(function(value){
    if(value)
        {
        obj.a=0.01;
    }else{
        obj.a=0;
    }
})
tiaokong.close();
const guangzhao = gui.addFolder("光照");
// guangzhao.add(material,'shininess',1,5000).name("高光指数").step(1);

const renderer = new THREE.WebGLRenderer({
    antialias: true,
});
guangzhao.add(obj,'light',1,10).name("光照强度").step(0.1).onChange(function(value){
    directionalLight.intensity=value;
})
guangzhao.close();



renderer.setSize( window.innerWidth, window.innerHeight );
// document.body.appendChild( renderer.domElement );
const controls = new OrbitControls( camera, renderer.domElement );
controls.addEventListener('change', function () {
    renderer.render(scene, camera); //执行渲染操作
});
const stats = new Stats();
document.body.appendChild(stats.domElement);
stats.setMode(0);
function render() {
    stats.update();
    mesh.rotateY(obj.a);
    renderer.render(scene, camera);
    requestAnimationFrame(render);
    // console.log("render+++");
}
render();
window.onresize = function () {
    // 重置渲染器输出画布canvas尺寸
    renderer.setSize(window.innerWidth, window.innerHeight);
    // 全屏情况下：设置观察范围长宽比aspect为窗口宽高比
    camera.aspect = window.innerWidth / window.innerHeight;
    // 渲染器执行render方法的时候会读取相机对象的投影矩阵属性projectionMatrix
    // 但是不会每渲染一帧，就通过相机的属性计算投影矩阵(节约计算资源)
    // 如果相机的一些属性发生了变化，需要执行updateProjectionMatrix ()方法更新相机的投影矩阵
    camera.updateProjectionMatrix();
};
export default renderer.domElement;
