// motor.js
import * as THREE from 'three';

export function initMotor(RmCabinetModelList) {
    const group = new THREE.Group();

    RmCabinetModelList.forEach((cabinet, index) => {
        const length = cabinet.length / 1000;
        const width = cabinet.width / 1000;
        const height = cabinet.height / 1000;
        const positionX = cabinet.positionX;
        const positionZ = cabinet.positionY;
        const orientation = cabinet.orientation;

        const jiguiGeometry = new THREE.BoxGeometry(length, height, width);
        const jiguitexture = new THREE.TextureLoader().load(require('./pic/背景图1.png'));
        const faceTexture = new THREE.TextureLoader().load(require('./pic/机柜门.png'));
        const jiguiMaterial = new THREE.MeshPhysicalMaterial({
            map: jiguitexture,
            antialias: true,
            metalness: 0.2,
            roughness: 0.7,
            color: 0xffffff,
            shininess: 1000,
            clearcoat: 1,
            opacity : 0.3,

        });
        const faceMaterial = new THREE.MeshPhysicalMaterial({
            map: faceTexture,
            antialias: true,
            metalness: 0.8,
            roughness: 0.8,
            color: 0xffffff,
            shininess: 1000,
        });
        let materials1 = [
            jiguiMaterial, // 前面
            jiguiMaterial, // 后面
            jiguiMaterial, // 顶部
            jiguiMaterial, // 底部
            jiguiMaterial, // 左侧
            faceMaterial,  // 右侧
        ];
        let materials = materials1;
        const jiguiMesh = new THREE.Mesh(jiguiGeometry, materials);
        jiguiMesh.position.set(positionX, height / 2, positionZ);
        let fangxiang = 1;
        if(orientation==='1'){//东
            fangxiang = 2
        }
        if(orientation==='3'){//南
            fangxiang = 1
        }
        if(orientation==='2'){//西
            fangxiang = 0
        }
        if(orientation==='4'){//北
            fangxiang = 3
        }
        jiguiMesh.rotateY(Math.PI/2 * fangxiang );
        jiguiMesh.userData.cabinetData = cabinet; // 将 cabinet 数据添加到 userData
        jiguiMesh.material.forEach(material => {
            material.transparent = true;
            material.opacity = 1.0;
            material.depthWrite = false;
        });
        // const edgesGeometry = new THREE.EdgesGeometry(jiguiGeometry);
        // const edgesMaterial = new THREE.LineBasicMaterial({ color: 0x000000, linewidth: 0.5 });
        // const edges = new THREE.LineSegments(edgesGeometry, edgesMaterial);
        // edges.position.copy(jiguiMesh.position);
        
        group.add(jiguiMesh);
        // group.add(edges);

        
    });
    group.name = 'motor';
    return group;
}