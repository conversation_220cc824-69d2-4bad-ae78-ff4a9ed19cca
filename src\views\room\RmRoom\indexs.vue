<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="120px">
      <el-form-item label="所属区域" prop="deptId">
        <treeselect v-model="queryParams.deptId" :options="deptOptions" :show-count="true" placeholder="请选择归属部门"
          style="width:205px" />
      </el-form-item>
      <el-form-item label="机房名称" prop="roomName">
        <el-input v-model="queryParams.roomName" placeholder="请输入机房名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="机房编码" prop="roomCode">
        <el-input v-model="queryParams.roomCode" placeholder="请输入机房编码" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="级别" prop="roomLevel">
        <el-select v-model="queryParams.roomLevel" placeholder="请选择级别" clearable>
          <el-option v-for="dict in dict.type.room_level" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option v-for="dict in dict.type.room_status" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['room:RmRoom:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
          v-hasPermi="['room:RmRoom:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDeletes"
          v-hasPermi="['room:RmRoom:removes']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
          v-hasPermi="['room:RmRoom:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row> -->

    <el-table v-loading="loading" :data="RmRoomList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column prop="" label="序号" width="80" align="center" type="index" :index="indexMethod" />
      <el-table-column label="所属区域" align="center" prop="deptId" />
      <el-table-column label="机房名称" align="center" prop="roomName" />
      <el-table-column label="机房编码" align="center" prop="roomCode" />
      <el-table-column label="级别" align="center" prop="roomLevel">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.room_level" :value="scope.row.roomLevel" />
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.room_status" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['room:RmRoom:edit']">选择</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
  </div>
</template>

<script>
import { listRmRoom, getRmRoom, delRmRoom, addRmRoom, updateRmRoom } from "@/api/room/RmRoom";
import { deptTreeSelect } from "@/api/system/user";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import RmCabinet from "@/views//cabinet/RmCabinet/index.vue";
export default {
  name: "RmRoom",
  dicts: ['room_status', 'room_level'],
  components: { Treeselect, RmCabinet },
  data() {
    return {
      //是否弹窗全屏
      dialogFull: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 机房管理表格数据
      RmRoomList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        status: null,
        remarks: null,
        deptId: null,
        roomName: null,
        roomCode: null,
        address: null,
        length: null,
        width: null,
        height: null,
        longitude: null,
        latitud: null,
        floorCount: null,
        roomLevel: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        status: [
          { required: true, message: "状态不能为空", trigger: "blur" }
        ],
        roomName: [
          { required: true, message: "机房名称不能为空", trigger: "blur" }
        ],
        length: [
          { required: true, message: "长度不能为空", trigger: "blur" }
        ],
        width: [
          { required: true, message: "宽度不能为空", trigger: "blur" }
        ],
        roomLevel: [
          { required: true, message: "级别不能为空", trigger: "change" }
        ]
      },
      deptOptions: undefined,
      openCab: false,
      dialogshowSearch: false,
      currentRoomInfo: {},
    };
  },
  created() {
    this.getList();
    this.getDeptTree();
  },
  methods: {
    /** 机柜编辑*/
    handleCabinet(val) {
      this.openCab = true;
      this.dialogFull = true;
      this.title = "机柜管理-"+val.roomName;
      this.currentRoomInfo = val;
    },
    /** 查询部门下拉树结构 */
    getDeptTree() {
      deptTreeSelect().then(response => {
        this.deptOptions = response.data;
      });
    },
    indexMethod(index) {
      return (
        index + (this.queryParams.pageNum - 1) * this.queryParams.pageSize + 1
      );
    },
    handleChange(val) {
      // console.log(val);
    },
    handleClick(tab, event) {
      // console.log(tab, event);
    },
    handleClose() {
      this.cancel();
    },
    /** 查询机房管理列表 */
    getList() {
      this.loading = true;
      listRmRoom(this.queryParams).then(response => {
        this.RmRoomList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.openCab = false;
      this.dialogFull = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        uuid: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        userId: null,
        status: "1",
        filetime: null,
        remarks: null,
        delFlag: null,
        deptId: null,
        roomName: null,
        roomCode: null,
        address: null,
        length: null,
        width: null,
        height: null,
        longitude: null,
        latitud: null,
        floorCount: null,
        roomLevel: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.uuid)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "新建";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.uuid || this.ids
      getRmRoom(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.uuid != null) {
            updateRmRoom(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addRmRoom(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDeletes(row) {
      const ids = row.uuid || this.ids;
      this.$modal.confirm('是否确认删除选中的数据项？').then(function () {
        return delRmRoom(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.uuid || this.ids;
      this.$modal.confirm('是否确认删除数据？').then(function () {
        return delRmRoom(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('room/RmRoom/export', {
        ...this.queryParams
      }, `导出明细_${new Date().getTime()}.xlsx`)
    },

  }
};
</script>
<style lang="scss" scoped></style>
