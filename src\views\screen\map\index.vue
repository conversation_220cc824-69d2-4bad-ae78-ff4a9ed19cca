<template>
  <div class="map-area" :id="mapId"></div>
</template>
<script>
import { loadBMap } from "@/api/map3";
// import { listSpStoppagemap } from "@/api/Stoppage/SpStoppage";
export default {
  data() {
    return {
      mapId: "BMap-" + parseInt(Date.now() + Math.random()),
      myMap: null,
      map:null,
        // 故障管理表格数据
      SpStoppageList: [],
      // 查询参数
      queryParams: {
        stBranch: null,
        stQyzj: null,
        stDwdw: null,
        stStartime: null,
        stLevel: null,
        stStyle: null,
        stNewtstyle: null,
        stState: 'N',
        stStoptime: null,
        stReason: null,
        stIsyinhuan: null,
        stYjtime: null,
        stStopyhtime: null,
      },

    };
  },
  mounted() {
    // this.getNowTime();
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      this.loading = true;
      // listSpStoppagemap(this.queryParams).then(response => {
      //   this.SpStoppageList = response.rows;
      //   // this.SpStoppageList={};
      //   this.initMap(this.SpStoppageList);
      //   this.loading = false;
      // });
    },
    initMap(datelist) {
      loadBMap()
        .then(() => {
          // datelist
          var map = new BMap.Map(this.mapId, {enableMapClick:false},{minZoom:6,maxZoom:19});
          if(datelist.length>0){
            map.centerAndZoom(new BMap.Point(datelist[0].stLongitude, datelist[0].stLatitude), 14);
          }else{
            map.centerAndZoom(new BMap.Point(114.170988, 36.691837), 14);
          }
         
          map.enableScrollWheelZoom(true);
          map.addControl(
            new BMap.MapTypeControl({
              mapTypes: [BMAP_NORMAL_MAP, BMAP_HYBRID_MAP]
            })
          );
          var points1 = [];  // 添加海量点数据
          var points2 = [];  // 添加海量点数据
          for (var i = 0; i < datelist.length; i++) {
            // points1.push(new BMap.Point(datelist[i].stLongitude, datelist[i].stLatitude));
            if(datelist[i].stState){
              if(datelist[i].stState=='N'){
                points1.push(new BMap.Point(datelist[i].stLongitude, datelist[i].stLatitude));
              }else if(datelist[i].stState=='Y'){
                points2.push(new BMap.Point(datelist[i].stLongitude, datelist[i].stLatitude));
              }
            }
          }
          // var color='#d340c3';
          var options1 = {
              size: BMAP_POINT_SIZE_NORMAL,
              color: '#DC143C'
          }
          var options2 = {
              size: BMAP_POINT_SIZE_NORMAL,
              color: '#0000FF'
          }
            changePointsColor(points1,options1);
            changePointsColor(points2,options2);
          function changePointsColor(points,options){
              var pointCollection = new BMap.PointCollection(points, options);  // 初始化PointCollection
              map.addOverlay(pointCollection);  // 添加Overlay
              clickPoints(pointCollection);  
          }
            function clickPoints(pointCollection){
             pointCollection.addEventListener('click', function (e) {
              //循环查出值
              var stInfo="";
              var stStartime="";
              for (var i = 0; i < datelist.length; i++) {
                if(datelist[i].stLongitude==e.point.lng&&datelist[i].stLatitude==e.point.lat){//经度==点击的,维度
                  var stInfo=datelist[i].stInfo;
                  var stStartime=datelist[i].stStartime;
                  break;
                }
              }
              var height=120;
              var point = new BMap.Point(e.point.lng, e.point.lat);
              var opts = {
                width: 200, // 信息窗口宽度
                height: height, // 信息窗口高度
                title:'', // 信息窗口标题
                enableMessage: false,//设置允许信息窗发送短息
              }
                 var Contents = `<div><span style='margin:5px;font-size:8px;'>发生时间：`+stStartime+`</span>
                <p style='margin:5px;line-height:1.5;height:10px;font-size:8px;'>故障详情：`+stInfo+`
                </p>
                </div>`;
              var infowindow = new BMap.InfoWindow(Contents, opts);
              map.openInfoWindow(infowindow, point);
            });
          }
        })
        .catch(err => {
          console.log(err);
        });
    }
  },
}
</script>
 
<style scoped>
.map-area {
  width: 100%;
  height: 100%;
  box-shadow:0 0 5px 5px rgb(46, 66, 96, 0.1);
  /* height: calc(100vh - 84px); */
  }
</style>