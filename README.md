## 开发

```bash
# 克隆项目
git clone https://<EMAIL>/a/HD22HEHDYF0011/tele-station-ui && (cd tele-station-ui && curl -kLo `git rev-parse --git-dir`/hooks/commit-msg https://<EMAIL>/tools/hooks/commit-msg; chmod +x `git rev-parse --git-dir`/hooks/commit-msg)

# 进入项目目录
cd 

# 安装依赖
npm install

# 建议不要直接使用 cnpm 安装依赖，会有各种诡异的 bug。可以通过如下操作解决 npm 下载速度慢的问题
npm install --registry=https://registry.npmmirror.com

# 启动服务
npm run dev
```

浏览器访问 http://localhost:80

## 发布

```bash
# 构建测试环境
npm run build:stage

# 构建生产环境
npm run build:prod
```