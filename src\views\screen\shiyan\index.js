import * as THREE from 'three';
console.log("three",THREE);
import Stats from 'three/examples/jsm/libs/stats.module.js';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
console.log("OrbitControls",OrbitControls)
// import { GUI } from 'three/examples/jsm/libs/lil-gui.module.min.js';
// const gui = new GUI();

// 使用 OrbitControls
// const controls = new OrbitControls(camera, renderer.domElement);
const scene = new THREE.Scene(); 
// const geometry = new THREE.BoxGeometry(1, 1, 1);
const geometry = new THREE.SphereGeometry(1);
const material = new THREE.MeshPhongMaterial({ 
    color: 0x00ff00,
    shininess:1000,
    // side: THREE.DoubleSide, //两面可见
    // opacity:0.7,//设置透明度
 });
const mesh = new THREE.Mesh(geometry, material);
mesh.position.set(0, 0, 0); 
console.log('查看当前屏幕设备像素比',window.devicePixelRatio);

const axesHelper = new THREE.AxesHelper(150);
// 随机创建大量的模型,测试渲染性能
const num = 500; //控制长方体模型数量
const meshs = [];
for (let i = 0; i < num; i++) {
    const geometry = new THREE.BoxGeometry(5, 5, 5);
    const material = new THREE.MeshLambertMaterial({
        color: 0x00ffff
    });
    const mesh = new THREE.Mesh(geometry, material);
    // 随机生成长方体xyz坐标
    const x = (Math.random() - 0.5) * 200
    const y = (Math.random() - 0.5) * 200
    const z = (Math.random() - 0.5) * 200
    mesh.position.set(x, y, z)
    meshs.push(mesh);
    scene.add(mesh); // 模型对象插入场景中
}
const ligth = new THREE.PointLight(0xffffff, 3);
ligth.position.set(5, 3, 5);
ligth.decay = 0;
const ligthHelper = new THREE.PointLightHelper(ligth);
// const ligth1 = new THREE.PointLight(0xffffff, 1);
// ligth1.position.set(-5, 3, 5);
// ligth1.decay = 0;
// const ligthHelper = new THREE.PointLightHelper(ligth1,ligth);
// scene.add(ligth1);
// const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
// // 设置光源的方向：通过光源position属性和目标指向对象的position属性计算
// directionalLight.position.set(8, 1, 5);
// // 方向光指向对象网格模型mesh，可以不设置，默认的位置是0,0,0
// directionalLight.target = mesh;
// const dirLightHelper = new THREE.DirectionalLightHelper(directionalLight, 5,0xff0000);


const width =window.innerWidth;
const height =window.innerHeight;
const camera = new THREE.PerspectiveCamera(75,width/height, 0.1, 100);
camera.position.set(3, 3, 3);
camera.lookAt(mesh.position);
// scene.add(mesh,camera,axesHelper,directionalLight,dirLightHelper);
scene.add(mesh,camera,axesHelper,ligth,ligthHelper);


//消除锯齿
const renderer = new THREE.WebGLRenderer({
    antialias:true,
    alpha:true,
  });
//设置像素比
renderer.setPixelRatio(window.devicePixelRatio);
renderer.setClearColor(0x888888);
renderer.setSize(width, height);
renderer.render(scene, camera);
document.body.appendChild(renderer.domElement);


const controls = new OrbitControls(camera, renderer.domElement);
controls.addEventListener('change', function () {
    renderer.render(scene, camera); //执行渲染操作
});
//创建stats对象
const stats = new Stats();
//stats.domElement:web页面上输出计算结果,一个div元素，
document.body.appendChild(stats.domElement);
stats.setMode(0);

function render() {
    stats.update();
    mesh.rotateY(0.01);
    for (let i = 0; i < num; i++) {
        meshs[i].rotateY(0.01);
    }
    renderer.render(scene, camera);
    requestAnimationFrame(render);
}
render();
window.onresize = function () {
    // 重置渲染器输出画布canvas尺寸
    renderer.setSize(window.innerWidth, window.innerHeight);
    // 全屏情况下：设置观察范围长宽比aspect为窗口宽高比
    camera.aspect = window.innerWidth / window.innerHeight;
    // 渲染器执行render方法的时候会读取相机对象的投影矩阵属性projectionMatrix
    // 但是不会每渲染一帧，就通过相机的属性计算投影矩阵(节约计算资源)
    // 如果相机的一些属性发生了变化，需要执行updateProjectionMatrix ()方法更新相机的投影矩阵
    camera.updateProjectionMatrix();
};
export default renderer.domElement;