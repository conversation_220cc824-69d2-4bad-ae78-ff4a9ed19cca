import request from '@/utils/request'

// 查询设备板卡管理列表
export function listRmEquipmentBoard(query) {
  return request({
    url: '/equipment/RmEquipmentBoard/list',
    method: 'get',
    params: query
  })
}

// 查询设备板卡管理详细
export function getRmEquipmentBoard(id) {
  return request({
    url: '/equipment/RmEquipmentBoard/' + id,
    method: 'get'
  })
}

// 新增设备板卡管理
export function addRmEquipmentBoard(data) {
  return request({
    url: '/equipment/RmEquipmentBoard',
    method: 'post',
    data: data
  })
}

// 修改设备板卡管理
export function updateRmEquipmentBoard(data) {
  return request({
    url: '/equipment/RmEquipmentBoard',
    method: 'put',
    data: data
  })
}

// 删除设备板卡管理
export function delRmEquipmentBoards(id) {
  return request({
    url: '/equipment/RmEquipmentBoard/' + id,
    method: 'delete'
  })
}

// 删除设备板卡管理
export function delRmEquipmentBoard(id) {
  return request({
    url: '/equipment/RmEquipmentBoard/Delete/' + id,
    method: 'delete'
  })
}
