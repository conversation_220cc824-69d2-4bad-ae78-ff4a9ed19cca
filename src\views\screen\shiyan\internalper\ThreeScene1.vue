<template>
    <div class="abc">
       <div class="anniu">按钮2</div>
    <div>{{ msg }}</div>
      <!-- <div class="cont red-visible"> -->
        <!-- <div class="cont" :class="{ 'red-visible': isThreeContainer2Visible }">
        <div class="lvse"></div>
        <div class="hse" v-show="isThreeContainer2Visible"></div> -->
      <!-- </div> -->
    </div>
</template>
<script>
export default {
    name: 'ThreeScene1',
    props: {
        msg: {
            type: String,
            default: '5555'
        }
    },
    created() {
        // this.$emit('aaa',this.a);
    },
    data() {
        return {

       };
    },

    methods: {
        // shiyan(){
        //     // console.log('msg',this.msg);
        //     this.$emit('aaa',this.msg);
        // },
    }
};
</script>

<style scoped>
/* 样式可以根据需要调整 */
.abc {
    padding: 0px;
    margin: 0px;
    width: 100%;
}
.anniu {
    cursor: pointer; /* 添加鼠标指针样式 */
    padding: 10px;
    background-color: #f0f0f0;
    border: 1px solid #ccc;
    text-align: center;
    width: 100px;
    height: 30px;
}
.cont {
    width: 100%;
    display: flex;
    border: 1px solid blue;
}
.lvse {
    background-color: green;
    height: 100vh; 
    width: 100%;
    border: 1px solid green;
    transition: width 0.3s ease; /* 添加过渡效果 */
}
.hse {
    background-color: red;
    width: 0%;
    height: 100vh;
    border: 1px solid red;
    transition: width 0.3s ease; /* 添加过渡效果 */
    overflow: hidden; /* 避免内容溢出 */
}
/* 当红色容器显示时，调整绿色容器和红色容器的宽度 */
.red-visible .lvse {
    width: 60%;
}

.red-visible .hse {
    width: 40%;
}
</style>