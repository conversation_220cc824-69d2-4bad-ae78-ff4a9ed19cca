import * as THREE from 'three';

export async function modelVis(model, newVal) {
    if (!model) {
        console.error('model is undefined or null in modelVis');
        return;
    }

    if (newVal) {
        model.traverse(function(child) {
        if (child.isMesh && child.material!=null) {
            const materials = Array.isArray(child.material) 
                    ? child.material 
                    : [child.material];
            materials.forEach(material => {
            material.transparent = true;
            material.depthWrite = false;
            material.opacity = 1.0;
            // model.remove(child.userData.edges);
            // delete child.userData.edges;

      });
      }
      if (child.userData.edges) {
        model.remove(child.userData.edges);
        delete child.userData.edges;
    }
    });
    }
    else {
        model.traverse(function(child) {
      if (child.isMesh) {
        child.material.forEach(material => {
          material.transparent = true;
          material.depthWrite = false;
          material.opacity = 0.3;
      });
          const edgesGeometry = new THREE.EdgesGeometry(child.geometry);
          const edgesMaterial = new THREE.LineBasicMaterial({ color: 0x003efa, linewidth: 1.3 });
          const edges = new THREE.LineSegments(edgesGeometry, edgesMaterial);
          const worldPosition = new THREE.Vector3();
          child.getWorldPosition(worldPosition);
          edges.position.copy(worldPosition);
          edges.rotation.copy(child.rotation);
          edges.name = 'edges';
          model.add(edges);

          child.userData.edges = edges;
          child.userData.edges.visible = !newVal;
      }

    });
    }}

function handleMaterial(material, isVisible) {
    if (Array.isArray(material)) {
        material.forEach(mat => {
            mat.transparent = true;
            mat.depthWrite = false;
            mat.opacity = isVisible ? 1.0 : 0.3;
        });
    } else if (material) {
        material.transparent = true;
        material.depthWrite = false;
        material.opacity = isVisible ? 1.0 : 0.3;
    }
}

export async function addInnerCube(model, newVal, RmCabinetModelList) {
    if (!model) {
        console.error('model is undefined or null');
        return;
    }

    if (!newVal) {
    let i = 0;
    model.traverse(function(child) {
        if (child.isMesh) {
        const cabinet = RmCabinetModelList[i++];
        const length = cabinet.length / 1300; // 假设单位是毫米，转换为米
        const width = cabinet.width / 1000; // 假设单位是毫米，转换为米
        const height = cabinet.height / 1500 + Math.random() * 1.3 - 0.9;
        const innerCubeGeometry = new THREE.BoxGeometry(length, height, width); // 较小的正方体
        let innerCubeMaterial;
        if(height<cabinet.height/2000)
        innerCubeMaterial = new THREE.MeshBasicMaterial({ color: 0x37ff00, transparent: true, opacity: 0.7 });
        else
        innerCubeMaterial = new THREE.MeshBasicMaterial({ color: 0xff0000, transparent: true, opacity: 0.7 });
        const innerCubeMesh = new THREE.Mesh(innerCubeGeometry, innerCubeMaterial);
        innerCubeMesh.position.copy(child.position);
        const worldPosition = new THREE.Vector3();
        child.getWorldPosition(worldPosition);
        innerCubeMesh.position.y = worldPosition.y + height / 2 - child.geometry.parameters.height/2;
        innerCubeMesh.rotation.copy(child.rotation)

        model.add(innerCubeMesh);
        if (!child.userData.innerCube) {
        child.userData.innerCube = innerCubeMesh;
        }
    }
    });
} else {    
    // 移除正方体
    model.traverse(function(child) {
    if (child.isMesh && child.userData.innerCube) {
        model.remove(child.userData.innerCube);
        delete child.userData.innerCube;
    }
    });
}
}