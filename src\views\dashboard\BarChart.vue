<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import resize from './mixins/resize'

const animationDuration = 6000

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    },
    barchartData: {
      type: Object,
      required: true
    },
  },
  data() {
    return {
      chart: null
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  watch: {
    barchartData: {
      deep: true,
      handler(val) {
        this.setOptions(val)
      }
    }
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons')
      this.setOptions(this.barchartData)
    },
    setOptions({ moneyData, ymoneyData,monthData,titlename} = {}) {
      this.chart.setOption({
        tooltip: {
        trigger: 'axis',
          axisPointer: {
            type: 'cross'
          },
          padding: [5, 10]
        },
        legend: {
          data: titlename

        },
        grid: {
           left: 10,
          right: 10,
          bottom: 20,
          top: 30,
          containLabel: true
        },
        xAxis: [{
          type: 'category',
          data: monthData,
          axisTick: {
            alignWithLabel: true,
            show: false
          }
        }],
        yAxis: [{
          type: 'value',
          axisTick: {
            show: false
          }
        }],
        series: [{
            name: titlename[0], itemStyle: {
            normal: {
              color: '#3888fa',
              lineStyle: {
                color: '#3888fa',
                width: 2
              }
            }
          },
          type: 'bar',
          barWidth: '30%',
          data: moneyData,
          label: {
            show: true,
            position: 'top'
          },
          animationDuration
        },
        {
         name: titlename[1], itemStyle: {
            normal: {
              color: '#9400D3',
              lineStyle: {
                color: '#9400D3',
                width: 2
              }
            }
          },
          type: 'bar',
          barWidth: '30%',
          data: ymoneyData,
          label: {
            show: true,
            position: 'top'
          },
          animationDuration
        }
        ]
      })
    }
  }
}
</script>
