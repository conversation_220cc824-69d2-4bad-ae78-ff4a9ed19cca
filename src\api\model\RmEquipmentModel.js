import request from '@/utils/request'

// 查询设备模板管理列表
export function listRmEquipmentModel(query) {
  return request({
    url: '/model/RmEquipmentModel/list',
    method: 'get',
    params: query
  })
}

// 查询设备模板管理详细
export function getRmEquipmentModel(id) {
  return request({
    url: '/model/RmEquipmentModel/' + id,
    method: 'get'
  })
}

// 新增设备模板管理
export function addRmEquipmentModel(data) {
  return request({
    url: '/model/RmEquipmentModel',
    method: 'post',
    data: data
  })
}

// 修改设备模板管理
export function updateRmEquipmentModel(data) {
  return request({
    url: '/model/RmEquipmentModel',
    method: 'put',
    data: data
  })
}

// 删除设备模板管理
export function delRmEquipmentModel(id) {
  return request({
    url: '/model/RmEquipmentModel/' + id,
    method: 'delete'
  })
}

// 查询设备模板管理列表
export function listRmEquipmentModels(query) {
  return request({
    url: '/model/RmEquipmentModel/lists',
    method: 'get',
    params: query
  })
}
