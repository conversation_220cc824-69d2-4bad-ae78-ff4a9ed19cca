<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="120px">
      <el-form-item label="所属区域" prop="deptId">
        <treeselect v-model="queryParams.deptId" :options="deptOptions" :show-count="true" placeholder="请选择归属部门"
          style="width:205px" />
      </el-form-item>

      <el-form-item label="名称" prop="cabinetName">
        <el-input v-model="queryParams.cabinetName" placeholder="请输入名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="编码" prop="cabinetCode">
        <el-input v-model="queryParams.cabinetCode" placeholder="请输入编码" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="所属机房" prop="roomName">
        <el-input v-model="queryParams.roomName" placeholder="请输入所属机房" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <!-- <el-form-item label="机柜总U数" prop="totalU">
        <el-input
          v-model="queryParams.totalU"
          placeholder="请输入机柜总U数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="机柜生产厂家" prop="manufacturer">
        <el-input v-model="queryParams.manufacturer" placeholder="请输入机柜生产厂家" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="机柜类型" prop="cabinetType">
        <el-select v-model="queryParams.cabinetType" placeholder="请选择机柜类型" clearable>
          <el-option v-for="dict in dict.type.cabinet_type" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option v-for="dict in dict.type.cabinet_status" :key="dict.value" :label="dict.label"
            :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['cabinet:RmCabinet:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
          v-hasPermi="['cabinet:RmCabinet:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDeletes"
          v-hasPermi="['cabinet:RmCabinet:removes']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
          v-hasPermi="['cabinet:RmCabinet:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="RmCabinetList" @selection-change="handleSelectionChange" border>
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column prop="" label="序号" width="80" align="center" type="index" :index="indexMethod" />
      <el-table-column label="所属区域" align="center" prop="deptId" width="120"/>
      <el-table-column label="名称" align="center" prop="cabinetName" />
      <el-table-column label="编码" align="center" prop="cabinetCode" />
      <el-table-column label="所属机房" align="center" prop="roomName" />
      <el-table-column label="机柜总U数" align="center" prop="totalU" width="120"/>
      <el-table-column label="机柜生产厂家" align="center" prop="manufacturer" width="120"/>
      <el-table-column label="机柜类型" align="center" prop="cabinetType"  width="120">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.cabinet_type" :value="scope.row.cabinetType" />
        </template>
      </el-table-column>
      <!-- <el-table-column label="长度" align="center" prop="length" />
      <el-table-column label="宽度" align="center" prop="width" />
      <el-table-column label="高度" align="center" prop="height" />
      <el-table-column label="所在机房位置x" align="center" prop="positionX" />
      <el-table-column label="所在机房位置y" align="center" prop="positionY" /> -->
      <el-table-column label="状态" align="center" prop="status" width="120">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.cabinet_status" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="120">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['cabinet:RmCabinet:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['cabinet:RmCabinet:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改机柜管理对话框 -->
    <el-dialog :title="title" :fullscreen="dialogFull" :visible.sync="open" width="70%" append-to-body
      :close-on-click-modal="false" @close="handleClose()">
      <template slot="title">
        <div class="avue-crud__dialog__header" style="border-bottom:1px solid #DCDFE6;padding-bottom:15px;">
          <span class="el-dialog__title">
            <span
              style="display:inline-block;background-color: #3478f5;width:3px;height:20px;margin-right:5px; float: left;margin-top:2px;margin-right:15px"></span>
            {{ title }}
          </span>
          <div class="avue-crud__dialog__menu" @click="dialogFull ? dialogFull = false : dialogFull = true">
            <i class="el-icon-full-screen"></i>
          </div>
        </div>
      </template>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="8">
            <el-form-item label="所属模板" prop="modelId">
              <el-select v-model="form.modelId" placeholder="请选择模板" filterable clearable @change="selectModel">
                <el-option v-for="(item, index) in RmCabinetModelList" :key="index" :label="item.cabinetName"
                  :value="item.uuid">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="名称" prop="cabinetName">
              <el-input v-model="form.cabinetName" placeholder="请输入名称" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="编码" prop="cabinetCode">
              <el-input v-model="form.cabinetCode" placeholder="请输入编码" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="机柜类型" prop="cabinetType">
              <el-select v-model="form.cabinetType" placeholder="请选择机柜类型">
                <el-option v-for="dict in dict.type.cabinet_type" :key="dict.value" :label="dict.label"
                  :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="所属机房" prop="roomName">
              <!-- <el-input v-model="form.roomName" placeholder="请输入所属机房" v-if="showSearch" /> -->
              <el-select v-model="form.roomName" placeholder="请选择所属机房" filterable clearable @change="selectRoom" v-if="showSearch">
                <el-option v-for="(item, index) in RmRoomList" :key="index" :label="item.roomName"
                  :value="item.uuid">
                </el-option>
              </el-select>
              <el-input v-model="form.roomName" placeholder="请输入所属机房" readonly v-else />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="机柜生产厂家" prop="manufacturer">
              <el-input v-model="form.manufacturer" placeholder="请输入机柜生产厂家" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="机柜总U数" prop="totalU">
              <el-input v-model="form.totalU" placeholder="请输入机柜总U数" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="长度(mm)" prop="length">
              <el-input v-model="form.length" placeholder="请输入长度" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="宽度(mm)" prop="width">
              <el-input v-model="form.width" placeholder="请输入宽度" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="高度(mm)" prop="height">
              <el-input v-model="form.height" placeholder="请输入高度" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="坐标x(m)" prop="positionX">
              <el-input v-model="form.positionX" placeholder="请输入所在机房位置x" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="坐标y(m)" prop="positionY">
              <el-input v-model="form.positionY" placeholder="请输入所在机房位置y" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="机柜朝向" prop="orientation">
              <el-select v-model="form.orientation" placeholder="请选择机柜朝向">
                <el-option v-for="dict in dict.type.cabinet_orientation" :key="dict.value" :label="dict.label"
                  :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注说明" prop="remarks">
              <el-input v-model="form.remarks" type="textarea" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="状态">
              <el-radio-group v-model="form.status">
                <el-radio v-for="dict in dict.type.cabinet_status" :key="dict.value" :label="dict.value">{{ dict.label
                  }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listRmCabinet, getRmCabinet, delRmCabinet, addRmCabinet, updateRmCabinet } from "@/api/cabinet/RmCabinet";
import { listRmCabinetModels } from "@/api/model/RmCabinetModel";
import { listRmRooms } from "@/api/room/RmRoom";
import { deptTreeSelect } from "@/api/system/user";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
export default {
  name: "RmCabinet",
  dicts: ['cabinet_type', 'cabinet_status', 'cabinet_orientation'],
  components: { Treeselect },
  props: {
    roomInfo: {
      type: Object,
      default: () => ({}) // 默认值为空对象
    }
  },
  data() {
    return {
      //是否弹窗全屏
      dialogFull: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 机柜管理表格数据
      RmCabinetList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        deptId: null,
        status: null,
        remarks: null,
        cabinetName: null,
        cabinetCode: null,
        roomId: null,
        roomName: null,
        totalU: null,
        manufacturer: null,
        cabinetType: null,
        length: null,
        width: null,
        height: null,
        positionX: null,
        positionY: null,
        orientation: null,
        modelId: null,
        modelName: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        status: [
          { required: true, message: "状态不能为空", trigger: "blur" }
        ],
        cabinetName: [
          { required: true, message: "名称不能为空", trigger: "blur" }
        ],
        roomId: [
          { required: true, message: "所属机房不能为空", trigger: "blur" }
        ],
        cabinetType: [
          { required: true, message: "机柜类型不能为空", trigger: "change" }
        ],
        length: [
          { required: true, message: "长度不能为空", trigger: "blur" }
        ],
        width: [
          { required: true, message: "宽度不能为空", trigger: "blur" }
        ],
        height: [
          { required: true, message: "高度不能为空", trigger: "blur" }
        ],
        positionX: [
          { required: true, message: "所在机房位置x不能为空", trigger: "blur" }
        ],
        positionY: [
          { required: true, message: "所在机房位置y不能为空", trigger: "blur" }
        ]
      },
      deptOptions: undefined,
      datas: {},
      roomInfo: {},
      // 机柜模板管理表格数据
      RmCabinetModelList: [],
      // 查询参数
      RoomqueryParams: {
        pageNum: 1,
        pageSize: 10,
        status: null,
        remarks: null,
        deptId: null,
        roomName: null,
        roomCode: null,
        address: null,
        length: null,
        width: null,
        height: null,
        longitude: null,
        latitud: null,
        floorCount: null,
        roomLevel: null
      },
      // 机房管理表格数据
      RmRoomList: [],
      roomInfo:{}
    };
  },
  created() {
    if (Object.keys(this.roomInfo).length > 0) {
      this.showSearch = false;
      this.queryParams.roomId = this.roomInfo.uuid;
    }
    this.getList();
    this.getDeptTree();
  },
  watch: {
    roomInfo: {
      deep: true,
      handler() {
        this.showSearch = false;
        this.queryParams.roomId = this.roomInfo.uuid;
        this.getList();
        this.getDeptTree();
      }
    },
  },
  methods: {
    /** 查询部门下拉树结构 */
    getDeptTree() {
      deptTreeSelect().then(response => {
        this.deptOptions = response.data;
      });
    },
    indexMethod(index) {
      return (
        index + (this.queryParams.pageNum - 1) * this.queryParams.pageSize + 1
      );
    },
    handleChange(val) {
      // console.log(val);
    },
    handleClick(tab, event) {
      // console.log(tab, event);
    },
    handleClose() {
      this.cancel();
    },
    /** 查询机柜管理列表 */
    getList() {
      this.loading = true;
      listRmCabinet(this.queryParams).then(response => {
        this.RmCabinetList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.dialogFull = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        uuid: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        userId: null,
        deptId: null,
        status: "1",
        filetime: null,
        remarks: null,
        delFlag: null,
        cabinetName: null,
        cabinetCode: null,
        roomId: null,
        roomName: null,
        totalU: null,
        manufacturer: null,
        cabinetType: null,
        length: null,
        width: null,
        height: null,
        positionX: null,
        positionY: null,
        orientation: null,
        modelId: null,
        modelName: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.uuid)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加";
      this.getModelLists();
      this.getRoomList();
      if (Object.keys(this.roomInfo).length > 0) {
        // this.showSearch = false;
        this.form.roomName = this.roomInfo.roomName;
        this.form.roomId = this.roomInfo.uuid;
      }
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.getModelLists();
      this.getRoomList();
      const id = row.uuid || this.ids
      getRmCabinet(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.uuid != null) {
            updateRmCabinet(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addRmCabinet(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDeletes(row) {
      const ids = row.uuid || this.ids;
      this.$modal.confirm('是否确认删除选中的数据项？').then(function () {
        return delRmCabinet(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.uuid || this.ids;
      this.$modal.confirm('是否确认删除数据？').then(function () {
        return delRmCabinet(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('cabinet/RmCabinet/export', {
        ...this.queryParams
      }, `导出明细_${new Date().getTime()}.xlsx`)
    },
    /** 查询机柜模板管理列表 */
    getModelLists() {
      this.loading = true;
      listRmCabinetModels().then(response => {
        this.RmCabinetModelList = response.rows;
        this.loading = false;
      });
    },
    selectModel(val) {
      const RmCabinetMode = this.RmCabinetModelList.find(item => item.uuid === val)
      this.form.modelName = RmCabinetMode.modelName
      this.form.modelId = RmCabinetMode.uuid
      this.form.cabinetType = RmCabinetMode.cabinetType
      this.form.manufacturer = RmCabinetMode.manufacturer
      this.form.totalU = RmCabinetMode.totalU
      this.form.length = RmCabinetMode.length
      this.form.width = RmCabinetMode.width
      this.form.height = RmCabinetMode.height
    },
    /** 查询机房管理列表 */
    getRoomList() {
      this.loading = true;
      listRmRooms().then(response => {
        this.RmRoomList = response.rows;
        // this.total = response.total;
        this.loading = false;
      });
    },
    selectRoom(val){
      const RoomMode = this.RmRoomList.find(item => item.uuid === val)
      this.form.roomName = RoomMode.roomName
      this.form.roomId = RoomMode.uuid
    }
  }
};
</script>
<style lang="scss" scoped>
.title {
  display: flex;
  justify-content: center;
  font-size: 30px;
  text-align: center;
  width: 100%;
  padding-bottom: 20px;
}
</style>
