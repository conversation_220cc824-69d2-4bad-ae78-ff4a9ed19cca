import * as THREE from 'three';

const lou = new THREE.Group();
lou.name = '高层';

for(let i=0;i<5;i++){
    const geometry = new THREE.BoxGeometry( 2, 6, 10 );
    geometry.translate(50/2,0,0,);
    const material = new THREE.MeshLambertMaterial({
        color:0xffffff,
    });
    const mesh = new THREE.Mesh( geometry, material );
    mesh.position.x=(i*4);
    mesh.name = "第"+(i+1)+"号楼";
    lou.add(mesh);
}

const lou2 = new THREE.Group();
lou2.name = '低层';
for(let i=0;i<5;i++){
    const geometry = new THREE.BoxGeometry( 2, 6, 8 );
    const material = new THREE.MeshLambertMaterial({
        color:0x00ffff,
    });
    const mesh = new THREE.Mesh( geometry, material );
    mesh.position.x=(i*3);
    mesh.name = "第"+(i+1)+"号楼";
    lou2.add(mesh);
}
lou2.position.set(0,0,5);
// lou.translateZ(5);
lou2.position.y = 15;
const meshAxesHelper = new THREE.AxesHelper( 50 );
lou2.add(meshAxesHelper);
const model = new THREE.Group();

const AxesHelper = new THREE.AxesHelper( 150 );
const worldPosition = new THREE.Vector3();
// 获取mesh的世界坐标，你会发现mesh的世界坐标受到父对象group的.position影响
lou2.getWorldPosition(worldPosition);
console.log('世界坐标',worldPosition);
console.log('本地坐标',lou2.position);

model.name = '小区';
model.add(lou,lou2,AxesHelper);
model.position.set(-5,0,2.5);
lou2.visible = true
export default model;