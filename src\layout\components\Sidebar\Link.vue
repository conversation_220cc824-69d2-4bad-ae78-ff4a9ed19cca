<template>
  <component :is="type" v-bind="linkProps(to)">
    <slot />
  </component>
</template>

<script>
import { isExternal } from '@/utils/validate'

export default {
  props: {
    to: {
      type: [String, Object],
      required: true
    }
  },
  computed: {
    isExternal() {
      return isExternal(this.to)
    },
    type() {
      if (this.isExternal) {
        return 'a'
      }
      return 'router-link'
    },
    //大屏
    isNewPage() {
      return this.to === "/screen";
    }
  },
  methods: {
    linkProps(to) {
      if (this.isExternal) {
        return {
          href: to,
          target: '_blank',
          rel: 'noopener'
        }
      }
      //大屏
      if (this.isNewPage) {  // this.isNewPage 为第三步 computed中添加的方法
        return {
          to: to.replace("/screen", "/dataVenterprise"), // "/dataVenterprise" 为
          target: '_blank'
        }
      }
      return {
        to: to
      }
    }
  }
}
</script>
