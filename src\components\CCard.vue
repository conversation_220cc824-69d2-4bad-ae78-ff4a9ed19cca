<template>
    <div class="card-box">
        <div class="card-box-title" v-if="title">
            {{ title }}
        </div>
        <div class="card-box-main" :style="{ height: title ? 'calc(100% - 45px)' : '100%' }">
            <slot></slot>
        </div>
    </div>
</template>
<script>
export default {
    props: {
        title: {
            type: String,
            default: ""
        }
    },
};
</script>
<style lang="scss" scoped>
.card-box {
    width: 100%;
    background: #fff;
    border-radius: 5px;
    box-shadow: 0px 3px 10px 1px rgba(0, 0, 0, 0.1);

    &-title {
        height: 44px;
        font-size: 16px;
        color: #000;
        line-height: 50px;
        border-bottom: 1px solid #E6EBF5;
        padding-left: 20px;
    }

    &-main {
        box-sizing: border-box;
        // padding: 10px;
    }
}
</style>
