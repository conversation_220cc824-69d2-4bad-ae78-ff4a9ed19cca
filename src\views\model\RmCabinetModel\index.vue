<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="120px">

      <el-form-item label="名称" prop="cabinetName">
        <el-input v-model="queryParams.cabinetName" placeholder="请输入名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <!-- <el-form-item label="型号" prop="cabinetCode">
        <el-input
          v-model="queryParams.cabinetCode"
          placeholder="请输入型号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="机柜生产厂家" prop="manufacturer">
        <el-input v-model="queryParams.manufacturer" placeholder="请输入机柜生产厂家" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="机柜类型" prop="cabinetType">
        <el-select v-model="queryParams.cabinetType" placeholder="请选择机柜类型" clearable>
          <el-option v-for="dict in dict.type.cabinet_type" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option v-for="dict in dict.type.cabinet_status" :key="dict.value" :label="dict.label"
            :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['cabinet:RmCabinetModel:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
          v-hasPermi="['cabinet:RmCabinetModel:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDeletes"
          v-hasPermi="['cabinet:RmCabinetModel:removes']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
          v-hasPermi="['cabinet:RmCabinetModel:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="RmCabinetModelList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column prop="" label="序号" width="80" align="center" type="index" :index="indexMethod" />
      <el-table-column label="名称" align="center" prop="cabinetName" />
      <el-table-column label="型号" align="center" prop="cabinetCode" />
      <el-table-column label="机柜生产厂家" align="center" prop="manufacturer" />
      <el-table-column label="机柜类型" align="center" prop="cabinetType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.cabinet_type" :value="scope.row.cabinetType" />
        </template>
      </el-table-column>
      <el-table-column label="长度(mm)" align="center" prop="length" />
      <el-table-column label="宽度(mm)" align="center" prop="width" />
      <el-table-column label="高度(mm)" align="center" prop="height" />
      <el-table-column label="机柜总U数" align="center" prop="totalU" />
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.cabinet_status" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['cabinet:RmCabinetModel:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['cabinet:RmCabinetModel:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改机柜模板管理对话框 -->
    <el-dialog :title="title" :fullscreen="dialogFull" :visible.sync="open" width="70%" append-to-body
      :close-on-click-modal="false" @close="handleClose()">
      <template slot="title">
        <div class="avue-crud__dialog__header" style="border-bottom:1px solid #DCDFE6;padding-bottom:15px;">
          <span class="el-dialog__title">
            <span
              style="display:inline-block;background-color: #3478f5;width:3px;height:20px;margin-right:5px; float: left;margin-top:2px;margin-right:15px"></span>
            {{ title }}
          </span>
          <div class="avue-crud__dialog__menu" @click="dialogFull ? dialogFull = false : dialogFull = true">
            <i class="el-icon-full-screen"></i>
          </div>
        </div>
      </template>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="8">
            <el-form-item label="名称" prop="cabinetName">
              <el-input v-model="form.cabinetName" placeholder="请输入名称" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="型号" prop="cabinetCode">
              <el-input v-model="form.cabinetCode" placeholder="请输入型号" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="机柜类型" prop="cabinetType">
              <el-select v-model="form.cabinetType" placeholder="请选择机柜类型">
                <el-option v-for="dict in dict.type.cabinet_type" :key="dict.value" :label="dict.label"
                  :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="长度(mm)" prop="length">
              <el-input v-model="form.length" placeholder="请输入长度" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="宽度(mm)" prop="width">
              <el-input v-model="form.width" placeholder="请输入宽度" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="高度(mm)" prop="height">
              <el-input v-model="form.height" placeholder="请输入高度" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="生产厂家" prop="manufacturer">
              <el-input v-model="form.manufacturer" placeholder="请输入机柜生产厂家" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="机柜总U数" prop="totalU">
              <el-input v-model="form.totalU" placeholder="请输入机柜总U数" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="状态">
              <el-radio-group v-model="form.status">
                <el-radio v-for="dict in dict.type.cabinet_status" :key="dict.value" :label="dict.value">{{ dict.label
                  }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注说明" prop="remarks">
              <el-input v-model="form.remarks" type="textarea" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listRmCabinetModel, getRmCabinetModel, delRmCabinetModel, addRmCabinetModel, updateRmCabinetModel } from "@/api/model/RmCabinetModel";

export default {
  name: "RmCabinetModel",
  dicts: ['cabinet_type', 'cabinet_status'],
  data() {
    return {
      //是否弹窗全屏
      dialogFull: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 机柜模板管理表格数据
      RmCabinetModelList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        status: null,
        remarks: null,
        cabinetName: null,
        cabinetCode: null,
        totalU: null,
        manufacturer: null,
        cabinetType: null,
        length: null,
        width: null,
        height: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        cabinetName: [
          { required: true, message: "名称不能为空", trigger: "blur" }
        ],
        cabinetCode: [
          { required: true, message: "编码不能为空", trigger: "blur" }
        ],
        cabinetType: [
          { required: true, message: "机柜类型不能为空", trigger: "change" }
        ],
        length: [
          { required: true, message: "长度不能为空", trigger: "blur" }
        ],
        width: [
          { required: true, message: "宽度不能为空", trigger: "blur" }
        ],
        height: [
          { required: true, message: "高度不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    indexMethod(index) {
      return (
        index + (this.queryParams.pageNum - 1) * this.queryParams.pageSize + 1
      );
    },
    handleChange(val) {
      // console.log(val);
    },
    handleClick(tab, event) {
      // console.log(tab, event);
    },
    handleClose() {
      this.cancel();
    },
    /** 查询机柜模板管理列表 */
    getList() {
      this.loading = true;
      listRmCabinetModel(this.queryParams).then(response => {
        this.RmCabinetModelList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.dialogFull = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        uuid: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        userId: null,
        deptId: null,
        status: "1",
        filetime: null,
        remarks: null,
        delFlag: null,
        cabinetName: null,
        cabinetCode: null,
        totalU: null,
        manufacturer: null,
        cabinetType: null,
        length: null,
        width: null,
        height: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.uuid)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "新建";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.uuid || this.ids
      getRmCabinetModel(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.uuid != null) {
            updateRmCabinetModel(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addRmCabinetModel(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDeletes(row) {
      const ids = row.uuid || this.ids;
      this.$modal.confirm('是否确认删除选中的数据项？').then(function () {
        return delRmCabinetModel(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.uuid || this.ids;
      this.$modal.confirm('是否确认删除数据？').then(function () {
        return delRmCabinetModel(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('model/RmCabinetModel/export', {
        ...this.queryParams
      }, `导出明细_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
<style lang="scss" scoped></style>
